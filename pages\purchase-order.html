<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Orders - Inventory Management System</title>
    <meta name="description" content="Manage and track purchase orders in your inventory management system">

    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/layout.css">
    <link rel="stylesheet" href="../styles/components.css">

    <style>
        /* Modern Purchase Order Styles */
       
        .hero-section {
            background: white;
            color: #000000;
            padding: var(--spacing-sm);
            border-radius: 16px;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-xs) 0;
        }

        .hero-subtitle {
            font-size: var(--font-size-sm);
            opacity: 0.8;
            margin: 0;
        }
        /* Innovative Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-2xl);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-sm);
            margin-top: var(--spacing-sm);
            /* height: 100px; */
        }

        .metric-card {
            background: var(--color-white);
            border-radius: 20px;
            padding: var(--spacing-xl);
            position: relative;
            overflow: hidden;
            border: 1px solid var(--color-border);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-color, #000) 0%, transparent 100%);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.4s ease;
        }

        .metric-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .metric-card:hover::before {
            transform: scaleX(1);
        }

        .metric-card.total-orders { --accent-color: #2196F3; }
        .metric-card.pending { --accent-color: #FF9800; }
        .metric-card.approved { --accent-color: #4CAF50; }
        .metric-card.total-value { --accent-color: #9C27B0; }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-lg);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--accent-color, #000) 0%, rgba(var(--accent-color-rgb, 0,0,0), 0.8) 100%);
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 8px 16px rgba(var(--accent-color-rgb, 0,0,0), 0.3);
        }

        .metric-trend {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: var(--font-size-sm);
            font-weight: 600;
        }

        .metric-trend.positive { color: #4CAF50; }
        .metric-trend.negative { color: #F44336; }
        .metric-trend.neutral { color: var(--color-tertiary); }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--color-primary);
            margin-bottom: var(--spacing-xs);
            line-height: 1;
        }

        .metric-label {
            font-size: var(--font-size-base);
            color: var(--color-secondary);
            font-weight: 600;
            margin-bottom: var(--spacing-md);
        }

        .metric-progress {
            height: 6px;
            background: var(--color-light);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: var(--spacing-sm);
        }

        .metric-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-color, #000) 0%, rgba(var(--accent-color-rgb, 0,0,0), 0.7) 100%);
            border-radius: 3px;
            transition: width 1s ease-out;
            transform-origin: left;
        }

        .metric-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-size-sm);
            color: var(--color-tertiary);
        }

        /* Enhanced Page Actions */
        .page-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
            gap: var(--spacing-md);
            background: var(--color-white);
            padding: var(--spacing-lg);
            border-radius: 16px;
            border: 1px solid var(--color-border);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .actions-left {
            display: flex;
            gap: var(--spacing-md);
        }

        .actions-right {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .filter-container {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .filter-select {
            min-width: 150px;
            border-radius: 12px;
            border: 2px solid var(--color-border);
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced Button Styles */
        .btn-enhanced {
            position: relative;
            overflow: hidden;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-enhanced:hover::before {
            left: 100%;
        }

        .btn-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        /* CSS Variables for accent colors */
        .total-orders { --accent-color-rgb: 33, 150, 243; }
        .pending { --accent-color-rgb: 255, 152, 0; }
        .approved { --accent-color-rgb: 76, 175, 80; }
        .total-value { --accent-color-rgb: 156, 39, 176; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }

            .metric-card {
                padding: var(--spacing-lg);
            }

            .metric-value {
                font-size: 2rem;
            }

            .page-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .actions-left,
            .actions-right {
                justify-content: center;
            }

            .filter-container {
                flex-direction: column;
            }

            .filter-select {
                min-width: auto;
                width: 100%;
            }
        }

        /* Animation keyframes */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .metric-card {
            animation: fadeInScale 0.6s ease-out;
        }

        .metric-card:nth-child(1) { animation-delay: 0.1s; }
        .metric-card:nth-child(2) { animation-delay: 0.2s; }
        .metric-card:nth-child(3) { animation-delay: 0.3s; }
        .metric-card:nth-child(4) { animation-delay: 0.4s; }


        /*-------------------NEW----------------------------------*/

/* Hero Section */
.hero-section {
    background: var(--color-white); color: var(--color-primary); padding: var(--spacing-lg); 
    border-radius: var(--border-radius-lg); position: relative; overflow: hidden; margin-bottom: var(--spacing-md); 
    box-shadow: var(--shadow-sm); border: 1px solid var(--color-border);
}
.hero-content { position: relative; z-index: 1; }
.hero-title { font-size: 1.5rem; font-weight: 700; margin: 0 0 var(--spacing-xs) 0; color: var(--color-primary); }
.hero-subtitle { font-size: var(--font-size-sm); opacity: 0.8; margin: 0; color: var(--color-secondary); }

/* Metrics Grid - Compacted */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); 
    gap: var(--spacing-md); 
    margin-bottom: var(--spacing-lg); 
}
.metric-card {
    background: var(--color-white); border-radius: var(--border-radius-lg); 
    padding: var(--spacing-md); position: relative; overflow: hidden;
    border: 1px solid var(--color-border); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer; min-height: 100px; display: flex; flex-direction: column;
    justify-content: space-between; 
}
.metric-card::before { 
    content: ''; position: absolute; top: 0; left: 0; width: 4px; height: 100%;
    background: var(--accent-color, var(--color-primary));
    transition: transform 0.4s ease; transform: scaleY(0.8); opacity: 0;
}
.metric-card:hover::before { transform: scaleY(1); opacity: 1;}
.metric-card:hover { transform: translateY(-4px); box-shadow: var(--shadow-lg); }
.metric-card.total-orders { --accent-color: #2196F3; --accent-color-rgb: 33,150,243; }
.metric-card.pending { --accent-color: #FF9800; --accent-color-rgb: 255,152,0; }
.metric-card.approved { --accent-color: #4CAF50; --accent-color-rgb: 76,175,80; }
.metric-card.total-value { --accent-color: #9C27B0; --accent-color-rgb: 156,39,176; }
.metric-header {
    display: flex; justify-content: space-between; align-items: flex-start; 
    margin-bottom: var(--spacing-sm); 
}
.metric-icon {
    width: 36px; height: 36px; border-radius: var(--border-radius); 
    display: flex; align-items: center; justify-content: center;
    background: var(--accent-color, var(--color-primary));
    color: white; font-size: 1.1rem; 
    box-shadow: 0 4px 8px rgba(var(--accent-color-rgb, 0,0,0), 0.2); 
    flex-shrink: 0;
}
.metric-trend {
    display: flex; align-items: center; gap: var(--spacing-xs);
    font-size: var(--font-size-xs); font-weight: 600;
    padding: 2px 4px; border-radius: var(--border-radius-sm);
    background-color: rgba(var(--accent-color-rgb, 0,0,0), 0.1);
    color: var(--accent-color, var(--color-primary));
    margin-left: var(--spacing-sm);
}
.metric-trend .material-icons { font-size: 0.9rem; }
.metric-trend.positive { --accent-color: var(--color-success); --accent-color-rgb: 76,175,80; }
.metric-trend.negative { --accent-color: var(--color-error); --accent-color-rgb: 244,67,54; }
.metric-content { margin-bottom: var(--spacing-xs); }
.metric-value {
    font-size: 1.75rem; font-weight: 700; color: var(--color-primary);
    margin-bottom: 0; line-height: 1.1; white-space: nowrap;
}
.metric-label {
    font-size: var(--font-size-xs); color: var(--color-secondary);
    font-weight: 500; margin-bottom: var(--spacing-sm); white-space: nowrap;
}
.metric-progress {
    height: 4px; background: var(--color-light); border-radius: 2px;
    overflow: hidden; margin-top: auto; 
}
.metric-progress-bar {
    height: 100%; background: var(--accent-color, var(--color-primary));
    border-radius: 2px; transition: width 0.8s ease-out;
}
.metric-details { display: none; }


/* Enhanced Page Actions & Advanced Filters */
.page-actions-and-filters {
    background: var(--color-white); padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg); border: 1px solid var(--color-border); 
    box-shadow: var(--shadow-sm); margin-bottom: var(--spacing-lg);
}
.page-actions-header {
    display: flex; justify-content: space-between; align-items: center; 
    margin-bottom: var(--spacing-lg); flex-wrap: wrap; gap: var(--spacing-md);
}
.actions-left { display: flex; gap: var(--spacing-sm); }
.actions-right-main { display: flex; gap: var(--spacing-sm); align-items: center; flex-wrap: wrap; }

.advanced-filters-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Adjusted minmax for fewer filters */
    gap: var(--spacing-md);
    align-items: flex-end; 
    margin-bottom: var(--spacing-lg);
}
.filter-group { display: flex; flex-direction: column; gap: var(--spacing-xs); }
.filter-group label { font-size: var(--font-size-xs); color: var(--color-tertiary); font-weight: 500; }
.filter-input, .filter-select { /* .filter-input is no longer used here directly, but style kept for consistency if re-added */
    width: 100%;
    padding: var(--spacing-sm); 
    border-radius: var(--border-radius); border: 1px solid var(--color-border); 
    font-size: var(--font-size-sm); background-color: var(--color-white);
    transition: border-color var(--transition), box-shadow var(--transition);
}
.filter-input:focus, .filter-select:focus { 
    border-color: var(--color-primary); 
    box-shadow: 0 0 0 2px rgba(0,0,0,0.1); outline: none; 
}
.filter-buttons { display: flex; gap: var(--spacing-sm); margin-top: var(--spacing-md); }


.btn-enhanced {
    padding: var(--spacing-sm) var(--spacing-md); font-size: var(--font-size-sm);
    position: relative; overflow: hidden; border-radius: var(--border-radius); font-weight: 500; 
    transition: all 0.3s ease; box-shadow: var(--shadow-sm); border: 1px solid transparent;
    display: inline-flex; align-items: center; gap: var(--spacing-sm); cursor: pointer;
}
.btn-enhanced.btn-primary { background-color: var(--color-primary); color: var(--color-white); }
.btn-enhanced.btn-primary:hover { background-color: var(--color-secondary); }
.btn-enhanced.btn-secondary { background-color: var(--color-white); color: var(--color-primary); border-color: var(--color-border); }
.btn-enhanced.btn-secondary:hover { background-color: var(--color-hover); }
.btn-enhanced.btn-success { background-color: var(--color-success); color: var(--color-white); }
.btn-enhanced.btn-success:hover { background-color: #388e3c; }
.btn-enhanced.btn-light { background-color: var(--color-light); color: var(--color-primary); border-color: var(--color-border); }
.btn-enhanced.btn-light:hover { background-color: #e0e0e0; }
.btn-enhanced .material-icons { font-size: 1.125rem; }


/* Purchase Orders Table */
.table-container {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden; 
    border: 1px solid var(--color-border);
}
.table-wrapper { overflow-x: auto; }
.table {
    width: 100%; border-collapse: collapse; 
    min-width: 1200px; 
}
.table th, .table td {
    padding: var(--spacing-sm) var(--spacing-md); 
    text-align: left; border-bottom: 1px solid var(--color-border);
    font-size: var(--font-size-sm); white-space: nowrap; 
}
.table th {
    background-color: var(--color-light); font-weight: 600;
    color: var(--color-secondary); text-transform: uppercase;
    letter-spacing: 0.5px; font-size: var(--font-size-xs); 
    position: sticky; top: 0; z-index: 10;
}
.table tbody tr:hover { background-color: var(--color-hover); }
.table tbody tr:last-child td { border-bottom: none; }
.table td { color: var(--color-secondary); }
.table td strong { font-weight: 600; color: var(--color-primary); }

.status-badge {
    display: inline-flex; align-items: center; padding: 3px 8px; 
    border-radius: var(--border-radius-lg); font-size: 0.7rem; 
    font-weight: 600; text-transform: capitalize; letter-spacing: 0.2px;
}
.status-badge.success { background-color: rgba(76, 175, 80, 0.1); color: var(--color-success); }
.status-badge.warning { background-color: rgba(255, 152, 0, 0.1); color: var(--color-warning); }
.status-badge.error { background-color: rgba(244, 67, 54, 0.1); color: var(--color-error); }
.status-badge.neutral { background-color: rgba(158, 158, 158, 0.1); color: var(--color-tertiary); } 
.status-badge.info { background-color: rgba(33, 150, 243, 0.1); color: var(--color-info); } 


.action-buttons { display: flex; gap: var(--spacing-xs); }
.action-btn {
    display: flex; align-items: center; justify-content: center;
    width: 28px; height: 28px; 
    border: none; border-radius: var(--border-radius-sm); background-color: transparent;
    color: var(--color-tertiary); cursor: pointer; transition: var(--transition);
}
.action-btn .material-icons { font-size: 1.1rem; }
.action-btn:hover { background-color: var(--color-hover); color: var(--color-primary); }

.attachment-cell { display: flex; align-items: center; gap: var(--spacing-xs); }
.attachment-cell .material-icons { font-size: 1rem; color: var(--color-tertiary); }

/* Pagination */
.pagination {
    display: flex; align-items: center; justify-content: space-between;
    padding: var(--spacing-md); background-color: var(--color-white);
    border-top: 1px solid var(--color-border); font-size: var(--font-size-sm);
}
.pagination-info { color: var(--color-tertiary); }
.pagination-controls { display: flex; gap: var(--spacing-xs); }
.pagination-btn {
    display: flex; align-items: center; justify-content: center; min-width: 32px; height: 32px; 
    padding: 0 var(--spacing-sm); border: 1px solid var(--color-border); border-radius: var(--border-radius-sm);
    background-color: var(--color-white); color: var(--color-tertiary); cursor: pointer; transition: var(--transition);
}
.pagination-btn:hover:not(:disabled) { background-color: var(--color-hover); color: var(--color-primary); }
.pagination-btn.active { background-color: var(--color-primary); color: var(--color-white); border-color: var(--color-primary); }
.pagination-btn:disabled { opacity: 0.6; cursor: not-allowed; }
.pagination-btn .material-icons { font-size: 1.25rem; }


/* Responsive Design Adjustments */
@media (max-width: 1200px) { 
    .main-content { padding: var(--spacing-md); }
    .metrics-grid { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); } 
    .advanced-filters-container { grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); } /* Adjusted for fewer filters */
}

@media (max-width: 768px) {
    .header { flex-direction: column; gap: var(--spacing-sm); padding: var(--spacing-sm); top:0; }
    .header-left, .header-center, .header-right { width: 100%; }
    .header-center { order: 3; } .header-right { order: 2; justify-content: space-between; }
    .navigation { top: 130px; }
    .nav-item span:not(.material-icons) { display: none; } 
    .nav-item { padding: var(--spacing-sm); }
    
    .hero-title { font-size: 1.25rem; }
    .hero-subtitle { font-size: var(--font-size-xs); }

    .metrics-grid { grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-sm); }
    .metric-card { padding: var(--spacing-sm); min-height: 80px; }
    .metric-icon { width: 28px; height: 28px; font-size: 1rem; border-radius: var(--border-radius-sm); }
    .metric-value { font-size: 1.25rem; }
    .metric-label { font-size: 0.65rem; }
    .metric-trend { font-size: 0.65rem; padding: 1px 3px;}
    .metric-trend .material-icons { font-size: 0.8rem; }

    .page-actions-header { flex-direction: column; align-items: stretch; }
    .actions-left, .actions-right-main { justify-content: center; width: 100%; }
    .advanced-filters-container { grid-template-columns: 1fr; } 
    .filter-buttons { flex-direction: column; }
    .filter-buttons .btn-enhanced { width: 100%; }


    .table th, .table td { font-size: var(--font-size-xs); padding: var(--spacing-sm); }
    .table th { font-size: 0.65rem; }
    .action-btn { width: 24px; height: 24px; }
    .action-btn .material-icons { font-size: 1rem; }
    .status-badge { font-size: 0.65rem; padding: 2px 6px;}
}

/* Animation keyframes */
@keyframes fadeInScale {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}
.metric-card { animation: fadeInScale 0.5s ease-out forwards; }
.metric-card:nth-child(1) { animation-delay: 0.05s; }
.metric-card:nth-child(2) { animation-delay: 0.1s; }
.metric-card:nth-child(3) { animation-delay: 0.15s; }
.metric-card:nth-child(4) { animation-delay: 0.2s; }

#no-results-row { display: none; } 
#no-results-row td { text-align: center; color: var(--color-tertiary); font-style: italic; padding: var(--spacing-xl); }

    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <span class="material-icons">inventory_2</span>
                    <div class="logo-text">
                        <h1>Inventory Management System</h1>
                        <p>COMPLETE BUSINESS SOLUTION</p>
                    </div>
                </div>
            </div>

            <div class="header-center">
                <div class="search-container">
                    <span class="material-icons">search</span>
                    <input type="text" placeholder="Search inventory, orders, invoices..." class="search-input">
                    <span class="search-shortcut">Ctrl+K</span>
                </div>
            </div>

            <div class="header-right">
                <button class="icon-button" title="Notifications">
                    <span class="material-icons">notifications</span>
                </button>
                <button class="icon-button" title="Language">
                    <span class="material-icons">language</span>
                </button>
                <button class="icon-button" title="Theme">
                    <span class="material-icons">light_mode</span>
                </button>
                <button class="icon-button" title="Settings">
                    <span class="material-icons">settings</span>
                </button>
                <div class="user-profile">
                    <div class="user-avatar">JD</div>
                    <div class="user-info">
                        <span class="user-name">John Doe</span>
                        <span class="user-role">System Administrator</span>
                    </div>
                    <span class="material-icons">expand_more</span>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="navigation">
            <div class="nav-container">
                <a href="dashboard.html" class="nav-item" data-page="dashboard">
                    <span class="material-icons">dashboard</span>
                    <span>Dashboard</span>
                </a>
                <a href="purchase-order.html" class="nav-item active" data-page="purchase-order">
                    <span class="material-icons">shopping_cart</span>
                    <span>Purchase Order</span>
                </a>
                <a href="purchase-invoice.html" class="nav-item" data-page="purchase-invoice">
                    <span class="material-icons">receipt</span>
                    <span>Purchase Invoice</span>
                </a>
                <a href="purchase-grn.html" class="nav-item" data-page="purchase-grn">
                    <span class="material-icons">assignment</span>
                    <span>Purchase GRN</span>
                </a>
                <a href="gdr-settlement.html" class="nav-item" data-page="gdr-settlement">
                    <span class="material-icons">account_balance</span>
                    <span>GDR Settlement</span>
                </a>
                <a href="po-cancellation.html" class="nav-item" data-page="po-cancellation">
                    <span class="material-icons">cancel</span>
                    <span>PO Cancellation</span>
                </a>
                <a href="stock-transfer-request.html" class="nav-item" data-page="stock-transfer-request">
                    <span class="material-icons">swap_horiz</span>
                    <span>Stock Transfer Request</span>
                </a>
                <a href="stock-receipt-grn.html" class="nav-item" data-page="stock-receipt-grn">
                    <span class="material-icons">inventory</span>
                    <span>Stock Receipt GRN</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Hero Section -->
            <!-- <div class="hero-section">
                <div class="hero-content">
                    <h1 class="hero-title">Purchase Orders</h1>
                    <p class="hero-subtitle">Advanced order management with real-time insights and intelligent analytics</p>
                </div>
            </div> -->
           
            <!-- Innovative Metrics Grid -->
            <div class="metrics-grid">
                <div class="metric-card total-orders" data-metric="total">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons">shopping_cart</span>
                        </div>
                        <div class="metric-trend positive">
                            <span class="material-icons">trending_up</span>
                            <span>+12.5%</span>
                        </div>
                    </div>
                    <div class="metric-value" data-target="1234">0</div>
                    <div class="metric-label">Total Orders</div>
                    <div class="metric-progress">
                        <div class="metric-progress-bar" style="width: 0%" data-width="85%"></div>
                    </div>
                    <div class="metric-details">
                        <span>This Month</span>
                        <span>Target: 1,450</span>
                    </div>
                </div>

                <div class="metric-card pending" data-metric="pending">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons">schedule</span>
                        </div>
                        <div class="metric-trend negative">
                            <span class="material-icons">trending_down</span>
                            <span>-5.2%</span>
                        </div>
                    </div>
                    <div class="metric-value" data-target="89">0</div>
                    <div class="metric-label">Pending Approval</div>
                    <div class="metric-progress">
                        <div class="metric-progress-bar" style="width: 0%" data-width="35%"></div>
                    </div>
                    <div class="metric-details">
                        <span>Awaiting Review</span>
                        <span>Avg: 2.3 days</span>
                    </div>
                </div>

                <div class="metric-card approved" data-metric="approved">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons">check_circle</span>
                        </div>
                        <div class="metric-trend positive">
                            <span class="material-icons">trending_up</span>
                            <span>+8.1%</span>
                        </div>
                    </div>
                    <div class="metric-value" data-target="1145">0</div>
                    <div class="metric-label">Approved Orders</div>
                    <div class="metric-progress">
                        <div class="metric-progress-bar" style="width: 0%" data-width="92%"></div>
                    </div>
                    <div class="metric-details">
                        <span>Success Rate</span>
                        <span>92.8%</span>
                    </div>
                </div>

                <div class="metric-card total-value" data-metric="value">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons">attach_money</span>
                        </div>
                        <div class="metric-trend positive">
                            <span class="material-icons">trending_up</span>
                            <span>+15.3%</span>
                        </div>
                    </div>
                    <div class="metric-value" data-target="2400000" data-format="currency">$0</div>
                    <div class="metric-label">Total Value</div>
                    <div class="metric-progress">
                        <div class="metric-progress-bar" style="width: 0%" data-width="78%"></div>
                    </div>
                    <div class="metric-details">
                        <span>YTD Performance</span>
                        <span>Budget: $3.1M</span>
                    </div>
                </div>
            </div>

            <!-- Enhanced Page Actions -->
            <div class="page-actions">
                <div class="actions-left">
                    <button class="btn btn-primary btn-enhanced" id="new-po-btn">
                        <span class="material-icons">add</span>
                        New Purchase Order
                    </button>
                    <!-- <button class="btn btn-secondary btn-enhanced" id="bulk-actions-btn">
                        <span class="material-icons">checklist</span>
                        Bulk Actions
                    </button> -->
                </div>

                <div class="actions-right">
                    <!-- <div class="filter-container">
                        <select class="filter-select form-input">
                            <option value="">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                            <option value="completed">Completed</option>
                        </select>
                        <select class="filter-select form-input">
                            <option value="">All Suppliers</option>
                            <option value="abc">ABC Suppliers Ltd.</option>
                            <option value="xyz">XYZ Trading Co.</option>
                            <option value="def">DEF Industries</option>
                        </select>
                        <select class="filter-select form-input">
                            <option value="">Date Range</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="quarter">This Quarter</option>
                        </select>
                    </div> -->
                    <button class="btn btn-secondary btn-enhanced">
                        <span class="material-icons">file_download</span>
                        Export
                    </button>
                    <button class="btn btn-secondary btn-enhanced" id="refresh-btn">
                        <span class="material-icons">refresh</span>
                        Refresh
                    </button>
                </div>
            </div>

            <!-- Purchase Orders Table -->
            <div class="table-container">
                <div class="table-wrapper">
                    <table class="table" id="purchaseOrdersTable">
                        <thead>
                            <tr>
                                <th>Actions</th>
                                <th>PO Number</th>
                                <th>Ver.</th>
                                <th>Fin.Year</th>
                                <th>Date</th>
                                <th>Manufacturer Name</th>
                                <th>Type of Purchase</th>
                                <th>Order Class</th>
                                <th>Status</th>
                                <th>Attachments</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view" title="View Details"><span class="material-icons">visibility</span></button>
                                        <button class="action-btn edit" title="Edit PO"><span class="material-icons">edit</span></button>
                                        <button class="action-btn" title="Download PO"><span class="material-icons">file_download</span></button>
                                    </div>
                                </td>
                                <td><strong>PO-2024-001</strong></td>
                                <td>1.0</td>
                                <td>2024-2025</td>
                                <td data-date="2024-01-15">2024-01-15</td>
                                <td>ABC Suppliers Ltd.</td>
                                <td>Raw Materials</td>
                                <td>Standard</td>
                                <td><span class="status-badge success">Approved</span></td>
                                <td class="attachment-cell"><span class="material-icons">attach_file</span> 2</td>
                                <td><strong>$12,500.00</strong></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view" title="View Details"><span class="material-icons">visibility</span></button>
                                        <button class="action-btn edit" title="Edit PO"><span class="material-icons">edit</span></button>
                                        <button class="action-btn" title="Download PO"><span class="material-icons">file_download</span></button>
                                    </div>
                                </td>
                                <td><strong>PO-2024-002</strong></td>
                                <td>1.1</td>
                                <td>2024-2025</td>
                                <td data-date="2024-01-14">2024-01-14</td>
                                <td>XYZ Trading Co.</td>
                                <td>Services</td>
                                <td>Urgent</td>
                                <td><span class="status-badge warning">Pending</span></td>
                                <td class="attachment-cell"><span class="material-icons">attach_file</span> 1</td>
                                <td><strong>$8,750.00</strong></td>
                            </tr>
                             <tr>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view" title="View Details"><span class="material-icons">visibility</span></button>
                                        <button class="action-btn edit" title="Edit PO"><span class="material-icons">edit</span></button>
                                        <button class="action-btn" title="Download PO"><span class="material-icons">file_download</span></button>
                                    </div>
                                </td>
                                <td><strong>PO-2023-088</strong></td>
                                <td>2.0</td>
                                <td>2023-2024</td>
                                <td data-date="2023-12-20">2023-12-20</td>
                                <td>Global Tech Inc.</td>
                                <td>Capital Goods</td>
                                <td>Project-Based</td>
                                <td><span class="status-badge success">Approved</span></td>
                                <td class="attachment-cell"><span class="material-icons">attach_file</span> 5</td>
                                <td><strong>$150,200.00</strong></td>
                            </tr>
                             <tr>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view" title="View Details"><span class="material-icons">visibility</span></button>
                                        <button class="action-btn edit" title="Edit PO"><span class="material-icons">edit</span></button>
                                        <button class="action-btn" title="Download PO"><span class="material-icons">file_download</span></button>
                                    </div>
                                </td>
                                <td><strong>PO-2024-003</strong></td>
                                <td>1.0</td>
                                <td>2024-2025</td>
                                <td data-date="2024-01-20">2024-01-20</td>
                                <td>Office Supplies Co.</td>
                                <td>Consumables</td>
                                <td>Standard</td>
                                <td><span class="status-badge neutral">Completed</span></td>
                               <td class="attachment-cell"><span class="material-icons">attach_file</span> 0</td>
                                <td><strong>$680.00</strong></td>
                            </tr>
                             <tr>
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn view" title="View Details"><span class="material-icons">visibility</span></button>
                                        <button class="action-btn edit" title="Edit PO"><span class="material-icons">edit</span></button>
                                        <button class="action-btn" title="Download PO"><span class="material-icons">file_download</span></button>
                                    </div>
                                </td>
                                <td><strong>PO-2024-004</strong></td>
                                <td>1.0</td>
                                <td>2024-2025</td>
                                <td data-date="2024-01-22">2024-01-22</td>
                                <td>Maintenance Solutions</td>
                                <td>Services</td>
                                <td>Recurring</td>
                                <td><span class="status-badge error">Rejected</span></td>
                                <td class="attachment-cell"><span class="material-icons">attach_file</span> 1</td>
                                <td><strong>$1,200.00</strong></td>
                            </tr>
                            <tr id="no-results-row">
                                <td colspan="11">No purchase orders found matching your criteria.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <div class="pagination-info">
                        Showing 1-5 of 1,234 POs
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled title="Previous Page"><span class="material-icons">chevron_left</span></button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">...</button>
                        <button class="pagination-btn">309</button>
                        <button class="pagination-btn" title="Next Page"><span class="material-icons">chevron_right</span></button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="../scripts/main.js"></script>
    <script src="../scripts/navigation.js"></script>
    <script src="../scripts/components.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize unified design system
            if (window.App) {
                window.App.init();
            }

            // Set current page for navigation
            if (window.Navigation) {
                window.Navigation.currentPage = 'purchase-order';
            }

            // Initialize purchase order specific features
            initializePurchaseOrderPage();
        });

        function initializePurchaseOrderPage() {
            // Initialize metric animations
            initializeMetricAnimations();

            // Initialize metric card interactions
            initializeMetricCardInteractions();

            // New Purchase Order button
            document.getElementById('new-po-btn').addEventListener('click', function() {
                if (window.Components) {
                    const modal = window.Components.createModal(
                        'Create New Purchase Order',
                        getPurchaseOrderForm(),
                        {
                            width: '800px',
                            footer: `
                                <button class="btn btn-secondary" onclick="window.Components.closeModal(this.closest('.modal-backdrop'))">Cancel</button>
                                <button class="btn btn-primary" onclick="savePurchaseOrder()">Create Purchase Order</button>
                            `
                        }
                    );
                }
            });

            // Bulk Actions button
            document.getElementById('bulk-actions-btn').addEventListener('click', function() {
                if (window.Components) {
                    window.Components.showToast('Bulk actions feature coming soon!', 'info');
                }
            });

            // Refresh button
            document.getElementById('refresh-btn').addEventListener('click', function() {
                refreshMetrics();
            });

            // Action buttons
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', handleTableAction);
            });

            // Filter functionality
            document.querySelectorAll('.filter-select').forEach(select => {
                select.addEventListener('change', applyFilters);
            });
        }

        // Initialize metric animations
        function initializeMetricAnimations() {
            // Animate metric values
            setTimeout(() => {
                document.querySelectorAll('.metric-value').forEach(element => {
                    const target = parseInt(element.getAttribute('data-target'));
                    const format = element.getAttribute('data-format');
                    animateValue(element, target, format);
                });

                // Animate progress bars
                document.querySelectorAll('.metric-progress-bar').forEach(bar => {
                    const targetWidth = bar.getAttribute('data-width');
                    setTimeout(() => {
                        bar.style.width = targetWidth;
                    }, 500);
                });
            }, 600);
        }

        // Enhanced value animation with formatting
        function animateValue(element, target, format = null) {
            let current = 0;
            const increment = target / 60; // 60 frames for smooth animation
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                if (format === 'currency') {
                    element.textContent = '$' + (current / 1000000).toFixed(1) + 'M';
                } else {
                    element.textContent = Math.floor(current).toLocaleString();
                }
            }, 16); // ~60fps
        }

        // Initialize metric card interactions
        function initializeMetricCardInteractions() {
            document.querySelectorAll('.metric-card').forEach(card => {
                card.addEventListener('click', function() {
                    const metric = this.getAttribute('data-metric');
                    showMetricDetails(metric);
                });

                // Add pulse effect on hover
                card.addEventListener('mouseenter', function() {
                    const icon = this.querySelector('.metric-icon');
                    icon.style.transform = 'scale(1.1)';
                    icon.style.transition = 'transform 0.3s ease';
                });

                card.addEventListener('mouseleave', function() {
                    const icon = this.querySelector('.metric-icon');
                    icon.style.transform = 'scale(1)';
                });
            });
        }

        // Show detailed metric information
        function showMetricDetails(metric) {
            const details = {
                total: {
                    title: 'Total Orders Analysis',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #f5f5f5; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #2196F3;">1,234</div>
                                <div style="font-size: 0.875rem; color: #666;">Total Orders</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #f5f5f5; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #4CAF50;">+12.5%</div>
                                <div style="font-size: 0.875rem; color: #666;">Growth Rate</div>
                            </div>
                        </div>
                        <p>Your purchase order volume has increased by 12.5% compared to last month, indicating strong business growth.</p>
                        <p><strong>Breakdown:</strong></p>
                        <ul>
                            <li>New Orders: 456</li>
                            <li>Recurring Orders: 778</li>
                            <li>Average Order Value: $1,945</li>
                        </ul>
                    `
                },
                pending: {
                    title: 'Pending Orders Analysis',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #fff3e0; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #FF9800;">89</div>
                                <div style="font-size: 0.875rem; color: #666;">Pending Orders</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #fff3e0; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #FF9800;">2.3</div>
                                <div style="font-size: 0.875rem; color: #666;">Avg Days</div>
                            </div>
                        </div>
                        <p>89 orders are currently pending approval with an average processing time of 2.3 days.</p>
                        <p><strong>Priority Actions:</strong></p>
                        <ul>
                            <li>12 orders over $10,000 need immediate review</li>
                            <li>23 orders from preferred suppliers</li>
                            <li>54 standard orders in queue</li>
                        </ul>
                    `
                },
                approved: {
                    title: 'Approved Orders Analysis',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #e8f5e8; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #4CAF50;">1,145</div>
                                <div style="font-size: 0.875rem; color: #666;">Approved Orders</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #e8f5e8; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #4CAF50;">92.8%</div>
                                <div style="font-size: 0.875rem; color: #666;">Success Rate</div>
                            </div>
                        </div>
                        <p>Excellent approval rate of 92.8% indicates efficient procurement processes and strong supplier relationships.</p>
                        <p><strong>Performance Metrics:</strong></p>
                        <ul>
                            <li>Average approval time: 1.2 days</li>
                            <li>Auto-approved orders: 67%</li>
                            <li>Manual review required: 33%</li>
                        </ul>
                    `
                },
                value: {
                    title: 'Total Value Analysis',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #f3e5f5; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #9C27B0;">$2.4M</div>
                                <div style="font-size: 0.875rem; color: #666;">Total Value</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #f3e5f5; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #9C27B0;">78%</div>
                                <div style="font-size: 0.875rem; color: #666;">Budget Used</div>
                            </div>
                        </div>
                        <p>Total purchase order value of $2.4M represents 78% of annual budget with strong ROI performance.</p>
                        <p><strong>Financial Breakdown:</strong></p>
                        <ul>
                            <li>Raw Materials: $1.2M (50%)</li>
                            <li>Equipment: $720K (30%)</li>
                            <li>Services: $480K (20%)</li>
                        </ul>
                    `
                }
            };

            if (window.Components && details[metric]) {
                window.Components.createModal(
                    details[metric].title,
                    details[metric].content,
                    { width: '600px' }
                );
            }
        }

        // Refresh metrics with animation
        function refreshMetrics() {
            if (window.Components) {
                window.Components.showToast('Refreshing metrics...', 'info');
            }

            // Add loading state
            document.querySelectorAll('.metric-card').forEach(card => {
                card.style.opacity = '0.6';
                card.style.transform = 'scale(0.98)';
            });

            // Simulate data refresh
            setTimeout(() => {
                document.querySelectorAll('.metric-card').forEach(card => {
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1)';
                });

                // Re-animate values
                initializeMetricAnimations();

                if (window.Components) {
                    window.Components.showToast('Metrics updated successfully!', 'success');
                }
            }, 1500);
        }

        function getPurchaseOrderForm() {
            return `
                <form class="form-container" id="po-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Supplier *</label>
                            <select class="form-input" required>
                                <option value="">Select Supplier</option>
                                <option value="abc">ABC Suppliers Ltd.</option>
                                <option value="xyz">XYZ Trading Co.</option>
                                <option value="def">DEF Industries</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Expected Delivery Date *</label>
                            <input type="date" class="form-input" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <textarea class="form-input" rows="3" placeholder="Purchase order description..."></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Items</label>
                        <div class="items-container">
                            <div class="item-row" style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr auto; gap: 1rem; align-items: end; margin-bottom: 1rem;">
                                <div>
                                    <label class="form-label">Item</label>
                                    <input type="text" class="form-input" placeholder="Item name">
                                </div>
                                <div>
                                    <label class="form-label">Quantity</label>
                                    <input type="number" class="form-input" placeholder="0">
                                </div>
                                <div>
                                    <label class="form-label">Unit Price</label>
                                    <input type="number" class="form-input" placeholder="0.00" step="0.01">
                                </div>
                                <div>
                                    <label class="form-label">Total</label>
                                    <input type="number" class="form-input" placeholder="0.00" readonly>
                                </div>
                                <button type="button" class="btn btn-secondary" style="height: 40px;">
                                    <span class="material-icons">add</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            `;
        }

        function savePurchaseOrder() {
            if (window.Components) {
                window.Components.showToast('Purchase order created successfully!', 'success');
                // Close modal
                const modal = document.querySelector('.modal-backdrop');
                if (modal) {
                    window.Components.closeModal(modal);
                }
            }
        }

        function handleTableAction(e) {
            const button = e.currentTarget;
            const action = button.classList.contains('view') ? 'view' :
                          button.classList.contains('edit') ? 'edit' :
                          button.classList.contains('delete') ? 'delete' : 'download';

            const row = button.closest('tr');
            const poNumber = row.querySelector('td:first-child strong').textContent;

            switch (action) {
                case 'view':
                    if (window.Components) {
                        window.Components.showToast(`Viewing details for ${poNumber}`, 'info');
                    }
                    break;
                case 'edit':
                    if (window.Components) {
                        window.Components.showToast(`Editing ${poNumber}`, 'info');
                    }
                    break;
                case 'delete':
                    if (window.Components) {
                        window.Components.confirm(
                            `Are you sure you want to delete ${poNumber}?`,
                            () => {
                                window.Components.showToast(`${poNumber} deleted successfully`, 'success');
                                row.remove();
                            }
                        );
                    }
                    break;
                case 'download':
                    if (window.Components) {
                        window.Components.showToast(`Downloading PDF for ${poNumber}`, 'info');
                    }
                    break;
            }
        }

        function applyFilters() {
            // TODO: Implement filtering logic
            if (window.Components) {
                window.Components.showToast('Filters applied', 'info');
            }
        }
    </script>
</body>
</html>
