<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Orders - Inventory Management System</title>
    <meta name="description" content="Manage and track purchase orders in your inventory management system">

    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/layout.css">
    <link rel="stylesheet" href="../styles/components.css">

    <style>
        /* Modern Purchase Order Styles */

        .hero-section {
            background: white;
            color: #000000;
            padding: var(--spacing-sm);
            border-radius: 16px;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 1.75rem;
            font-weight: 700;
            margin: 0 0 var(--spacing-xs) 0;
        }

        .hero-subtitle {
            font-size: var(--font-size-sm);
            opacity: 0.8;
            margin: 0;
        }
        /* Innovative Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-2xl);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-sm);
            margin-top: var(--spacing-sm);
            /* height: 100px; */
        }

        .metric-card {
            background: var(--color-white);
            border-radius: 20px;
            padding: var(--spacing-xl);
            position: relative;
            overflow: hidden;
            border: 1px solid var(--color-border);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-color, #000) 0%, transparent 100%);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.4s ease;
        }

        .metric-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .metric-card:hover::before {
            transform: scaleX(1);
        }

        .metric-card.total-orders { --accent-color: #2196F3; }
        .metric-card.pending { --accent-color: #FF9800; }
        .metric-card.approved { --accent-color: #4CAF50; }
        .metric-card.total-value { --accent-color: #9C27B0; }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-lg);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--accent-color, #000) 0%, rgba(var(--accent-color-rgb, 0,0,0), 0.8) 100%);
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 8px 16px rgba(var(--accent-color-rgb, 0,0,0), 0.3);
        }

        .metric-trend {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: var(--font-size-sm);
            font-weight: 600;
        }

        .metric-trend.positive { color: #4CAF50; }
        .metric-trend.negative { color: #F44336; }
        .metric-trend.neutral { color: var(--color-tertiary); }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--color-primary);
            margin-bottom: var(--spacing-xs);
            line-height: 1;
        }

        .metric-label {
            font-size: var(--font-size-base);
            color: var(--color-secondary);
            font-weight: 600;
            margin-bottom: var(--spacing-md);
        }

        .metric-progress {
            height: 6px;
            background: var(--color-light);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: var(--spacing-sm);
        }

        .metric-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-color, #000) 0%, rgba(var(--accent-color-rgb, 0,0,0), 0.7) 100%);
            border-radius: 3px;
            transition: width 1s ease-out;
            transform-origin: left;
        }

        .metric-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-size-sm);
            color: var(--color-tertiary);
        }

        /* Enhanced Page Actions */
        .page-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* margin-bottom: var(--spacing-xl); */
            flex-wrap: wrap;
            gap: var(--spacing-md);
            background: var(--color-white);
            padding: var(--spacing-sm);
            border-radius: 16px;
            border: 1px solid var(--color-border);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .actions-left {
            display: flex;
            gap: var(--spacing-md);
        }

        .actions-right {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .filter-container {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .filter-select {
            min-width: 150px;
            border-radius: 12px;
            border: 2px solid var(--color-border);
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(0, 0, 0, 0.1);
        }

        /* Enhanced Button Styles */
        .btn-enhanced {
            position: relative;
            overflow: hidden;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-enhanced:hover::before {
            left: 100%;
        }

        .btn-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        /* CSS Variables for accent colors */
        .total-orders { --accent-color-rgb: 33, 150, 243; }
        .pending { --accent-color-rgb: 255, 152, 0; }
        .approved { --accent-color-rgb: 76, 175, 80; }
        .total-value { --accent-color-rgb: 156, 39, 176; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }

            .metric-card {
                padding: var(--spacing-lg);
            }

            .metric-value {
                font-size: 2rem;
            }

            .page-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .actions-left,
            .actions-right {
                justify-content: center;
            }

            .filter-container {
                flex-direction: column;
            }

            .filter-select {
                min-width: auto;
                width: 100%;
            }
        }

        /* Animation keyframes */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .metric-card {
            animation: fadeInScale 0.6s ease-out;
        }

        .metric-card:nth-child(1) { animation-delay: 0.1s; }
        .metric-card:nth-child(2) { animation-delay: 0.2s; }
        .metric-card:nth-child(3) { animation-delay: 0.3s; }
        .metric-card:nth-child(4) { animation-delay: 0.4s; }


        /*-------------------NEW----------------------------------*/

/* Hero Section */
.hero-section {
    background: var(--color-white); color: var(--color-primary); padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg); position: relative; overflow: hidden; margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-sm); border: 1px solid var(--color-border);
}
.hero-content { position: relative; z-index: 1; }
.hero-title { font-size: 1.5rem; font-weight: 700; margin: 0 0 var(--spacing-xs) 0; color: var(--color-primary); }
.hero-subtitle { font-size: var(--font-size-sm); opacity: 0.8; margin: 0; color: var(--color-secondary); }

/* Metrics Grid - Compacted */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}
.metric-card {
    background: var(--color-white); border-radius: var(--border-radius-lg);
    padding: var(--spacing-md); position: relative; overflow: hidden;
    border: 1px solid var(--color-border); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer; min-height: 100px; display: flex; flex-direction: column;
    justify-content: space-between;
}
.metric-card::before {
    content: ''; position: absolute; top: 0; left: 0; width: 4px; height: 100%;
    background: var(--accent-color, var(--color-primary));
    transition: transform 0.4s ease; transform: scaleY(0.8); opacity: 0;
}
.metric-card:hover::before { transform: scaleY(1); opacity: 1;}
.metric-card:hover { transform: translateY(-4px); box-shadow: var(--shadow-lg); }
.metric-card.total-orders { --accent-color: #2196F3; --accent-color-rgb: 33,150,243; }
.metric-card.pending { --accent-color: #FF9800; --accent-color-rgb: 255,152,0; }
.metric-card.approved { --accent-color: #4CAF50; --accent-color-rgb: 76,175,80; }
.metric-card.total-value { --accent-color: #9C27B0; --accent-color-rgb: 156,39,176; }
.metric-card.rejected { --accent-color: #F44336; --accent-color-rgb: 244,67,54; }
.metric-header {
    display: flex; justify-content: space-between; align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}
.metric-icon {
    width: 36px; height: 36px; border-radius: var(--border-radius);
    display: flex; align-items: center; justify-content: center;
    background: var(--accent-color, var(--color-primary));
    color: white; font-size: 1.1rem;
    box-shadow: 0 4px 8px rgba(var(--accent-color-rgb, 0,0,0), 0.2);
    flex-shrink: 0;
}
.metric-trend {
    display: flex; align-items: center; gap: var(--spacing-xs);
    font-size: var(--font-size-xs); font-weight: 600;
    padding: 2px 4px; border-radius: var(--border-radius-sm);
    background-color: rgba(var(--accent-color-rgb, 0,0,0), 0.1);
    color: var(--accent-color, var(--color-primary));
    margin-left: var(--spacing-sm);
}
.metric-trend .material-icons { font-size: 0.9rem; }
.metric-trend.positive { --accent-color: var(--color-success); --accent-color-rgb: 76,175,80; }
.metric-trend.negative { --accent-color: var(--color-error); --accent-color-rgb: 244,67,54; }
.metric-content { margin-bottom: var(--spacing-xs); }
.metric-value {
    font-size: 1.75rem; font-weight: 700; color: var(--color-primary);
    margin-bottom: 0; line-height: 1.1; white-space: nowrap;
}
.metric-label {
    font-size: var(--font-size-xs); color: var(--color-secondary);
    font-weight: 500; margin-bottom: var(--spacing-sm); white-space: nowrap;
}
.metric-progress {
    height: 4px; background: var(--color-light); border-radius: 2px;
    overflow: hidden; margin-top: auto;
}
.metric-progress-bar {
    height: 100%; background: var(--accent-color, var(--color-primary));
    border-radius: 2px; transition: width 0.8s ease-out;
}
.metric-details { display: none; }


/* Enhanced Page Actions & Advanced Filters */
.page-actions-and-filters {
    background: var(--color-white); padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg); border: 1px solid var(--color-border);
    box-shadow: var(--shadow-sm); margin-bottom: var(--spacing-lg);
}
.page-actions-header {
    display: flex; justify-content: space-between; align-items: center;
    margin-bottom: var(--spacing-lg); flex-wrap: wrap; gap: var(--spacing-md);
}
.actions-left { display: flex; gap: var(--spacing-sm); }
.actions-right-main { display: flex; gap: var(--spacing-sm); align-items: center; flex-wrap: wrap; }

.advanced-filters-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Adjusted minmax for fewer filters */
    gap: var(--spacing-md);
    align-items: flex-end;
    margin-bottom: var(--spacing-lg);
}
.filter-group { display: flex; flex-direction: column; gap: var(--spacing-xs); }
.filter-group label { font-size: var(--font-size-xs); color: var(--color-tertiary); font-weight: 500; }
.filter-input, .filter-select { /* .filter-input is no longer used here directly, but style kept for consistency if re-added */
    width: 100%;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius); border: 1px solid var(--color-border);
    font-size: var(--font-size-sm); background-color: var(--color-white);
    transition: border-color var(--transition), box-shadow var(--transition);
}
.filter-input:focus, .filter-select:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(0,0,0,0.1); outline: none;
}
.filter-buttons { display: flex; gap: var(--spacing-sm); margin-top: var(--spacing-md); }


.btn-enhanced {
    padding: var(--spacing-sm) var(--spacing-md); font-size: var(--font-size-sm);
    position: relative; overflow: hidden; border-radius: var(--border-radius); font-weight: 500;
    transition: all 0.3s ease; box-shadow: var(--shadow-sm); border: 1px solid transparent;
    display: inline-flex; align-items: center; gap: var(--spacing-sm); cursor: pointer;
}
.btn-enhanced.btn-primary { background-color: var(--color-primary); color: var(--color-white); }
.btn-enhanced.btn-primary:hover { background-color: var(--color-secondary); }
.btn-enhanced.btn-secondary { background-color: var(--color-white); color: var(--color-primary); border-color: var(--color-border); }
.btn-enhanced.btn-secondary:hover { background-color: var(--color-hover); }
.btn-enhanced.btn-success { background-color: var(--color-success); color: var(--color-white); }
.btn-enhanced.btn-success:hover { background-color: #388e3c; }
.btn-enhanced.btn-light { background-color: var(--color-light); color: var(--color-primary); border-color: var(--color-border); }
.btn-enhanced.btn-light:hover { background-color: #e0e0e0; }
.btn-enhanced .material-icons { font-size: 1.125rem; }


/* Purchase Orders Table */
.table-container {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 1px solid var(--color-border);
}
.table-wrapper { overflow-x: auto; }
.table {
    width: 100%; border-collapse: collapse;
    min-width: 1200px;
}
.table th, .table td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left; border-bottom: 1px solid var(--color-border);
    font-size: var(--font-size-sm); white-space: nowrap;
}
.table th {
    background-color: var(--color-light); font-weight: 600;
    color: var(--color-secondary); text-transform: uppercase;
    letter-spacing: 0.5px; font-size: var(--font-size-xs);
    position: sticky; top: 0; z-index: 10;
}
.table tbody tr:hover { background-color: var(--color-hover); }
.table tbody tr:last-child td { border-bottom: none; }
.table td { color: var(--color-secondary); }
.table td strong { font-weight: 600; color: var(--color-primary); }

.status-badge {
    display: inline-flex; align-items: center; padding: 3px 8px;
    border-radius: var(--border-radius-lg); font-size: 0.7rem;
    font-weight: 600; text-transform: capitalize; letter-spacing: 0.2px;
}
.status-badge.success { background-color: rgba(76, 175, 80, 0.1); color: var(--color-success); }
.status-badge.warning { background-color: rgba(255, 152, 0, 0.1); color: var(--color-warning); }
.status-badge.error { background-color: rgba(244, 67, 54, 0.1); color: var(--color-error); }
.status-badge.neutral { background-color: rgba(158, 158, 158, 0.1); color: var(--color-tertiary); }
.status-badge.info { background-color: rgba(33, 150, 243, 0.1); color: var(--color-info); }


.action-buttons { display: flex; gap: var(--spacing-xs); }
.action-btn {
    display: flex; align-items: center; justify-content: center;
    width: 28px; height: 28px;
    border: none; border-radius: var(--border-radius-sm); background-color: transparent;
    color: var(--color-tertiary); cursor: pointer; transition: var(--transition);
}
.action-btn .material-icons { font-size: 1.1rem; }
.action-btn:hover { background-color: var(--color-hover); color: var(--color-primary); }

.attachment-cell { display: flex; align-items: center; gap: var(--spacing-xs); }
.attachment-cell .material-icons { font-size: 1rem; color: var(--color-tertiary); }

/* Pagination */
.pagination {
    display: flex; align-items: center; justify-content: space-between;
    padding: var(--spacing-md); background-color: var(--color-white);
    border-top: 1px solid var(--color-border); font-size: var(--font-size-sm);
}
.pagination-info { color: var(--color-tertiary); }
.pagination-controls { display: flex; gap: var(--spacing-xs); }
.pagination-btn {
    display: flex; align-items: center; justify-content: center; min-width: 32px; height: 32px;
    padding: 0 var(--spacing-sm); border: 1px solid var(--color-border); border-radius: var(--border-radius-sm);
    background-color: var(--color-white); color: var(--color-tertiary); cursor: pointer; transition: var(--transition);
}
.pagination-btn:hover:not(:disabled) { background-color: var(--color-hover); color: var(--color-primary); }
.pagination-btn.active { background-color: var(--color-primary); color: var(--color-white); border-color: var(--color-primary); }
.pagination-btn:disabled { opacity: 0.6; cursor: not-allowed; }
.pagination-btn .material-icons { font-size: 1.25rem; }


/* Responsive Design Adjustments */
@media (max-width: 1200px) {
    .main-content { padding: var(--spacing-md); }
    .metrics-grid { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }
    .advanced-filters-container { grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); } /* Adjusted for fewer filters */
}

@media (max-width: 768px) {
    .header { flex-direction: column; gap: var(--spacing-sm); padding: var(--spacing-sm); top:0; }
    .header-left, .header-center, .header-right { width: 100%; }
    .header-center { order: 3; } .header-right { order: 2; justify-content: space-between; }
    .navigation { top: 130px; }
    .nav-item span:not(.material-icons) { display: none; }
    .nav-item { padding: var(--spacing-sm); }

    .hero-title { font-size: 1.25rem; }
    .hero-subtitle { font-size: var(--font-size-xs); }

    .metrics-grid { grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--spacing-sm); }
    .metric-card { padding: var(--spacing-sm); min-height: 80px; }
    .metric-icon { width: 28px; height: 28px; font-size: 1rem; border-radius: var(--border-radius-sm); }
    .metric-value { font-size: 1.25rem; }
    .metric-label { font-size: 0.65rem; }
    .metric-trend { font-size: 0.65rem; padding: 1px 3px;}
    .metric-trend .material-icons { font-size: 0.8rem; }

    .page-actions-header { flex-direction: column; align-items: stretch; }
    .actions-left, .actions-right-main { justify-content: center; width: 100%; }
    .advanced-filters-container { grid-template-columns: 1fr; }
    .filter-buttons { flex-direction: column; }
    .filter-buttons .btn-enhanced { width: 100%; }


    .table th, .table td { font-size: var(--font-size-xs); padding: var(--spacing-sm); }
    .table th { font-size: 0.65rem; }
    .action-btn { width: 24px; height: 24px; }
    .action-btn .material-icons { font-size: 1rem; }
    .status-badge { font-size: 0.65rem; padding: 2px 6px;}
}

/* Animation keyframes */
@keyframes fadeInScale {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}
.metric-card { animation: fadeInScale 0.5s ease-out forwards; }
.metric-card:nth-child(1) { animation-delay: 0.05s; }
.metric-card:nth-child(2) { animation-delay: 0.1s; }
.metric-card:nth-child(3) { animation-delay: 0.15s; }
.metric-card:nth-child(4) { animation-delay: 0.2s; }
.metric-card:nth-child(5) { animation-delay: 0.25s; }

#no-results-row { display: none; }
#no-results-row td { text-align: center; color: var(--color-tertiary); font-style: italic; padding: var(--spacing-xl); }

/* Search Bar Styles */
.search-bar-container {
    position: relative;
    margin-right: var(--spacing-md);
}
.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--color-white);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
    transition: var(--transition);
    min-width: 280px;
}
.search-input-wrapper:focus-within {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(0,0,0,0.1);
}
.search-icon {
    color: var(--color-tertiary);
    font-size: 1.1rem;
    margin-right: var(--spacing-sm);
}
.search-input-table {
    border: none;
    outline: none;
    background: transparent;
    flex: 1;
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    padding: 0;
}
.search-input-table::placeholder {
    color: var(--color-tertiary);
}
.search-clear-btn {
    background: none;
    border: none;
    color: var(--color-tertiary);
    cursor: pointer;
    padding: 2px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    margin-left: var(--spacing-sm);
}
.search-clear-btn:hover {
    background: var(--color-hover);
    color: var(--color-primary);
}
.search-clear-btn .material-icons {
    font-size: 1rem;
}

/* Queue Management Styles */
.queue-management-container {
    background: var(--color-white);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border);
    box-shadow: var(--shadow-sm);
    /* margin-bottom: var(--spacing-lg); */
    overflow: hidden;
}
.queue-tabs {
    display: flex;
    background: var(--color-light);
    border-bottom: 1px solid var(--color-border);
}
.queue-tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: none;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--color-secondary);
    position: relative;
}
.queue-tab:hover {
    background: var(--color-hover);
    color: var(--color-primary);
}
.queue-tab.active {
    background: var(--color-white);
    color: var(--color-primary);
    font-weight: 600;
}
.queue-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--color-primary);
}
.queue-tab .material-icons {
    font-size: 1.1rem;
}
.queue-count {
    background: var(--color-primary);
    color: var(--color-white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--border-radius-lg);
    min-width: 20px;
    text-align: center;
}
.queue-tab.active .queue-count {
    background: var(--color-primary);
}
.queue-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-sm);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}
.queue-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--color-secondary);
    font-size: var(--font-size-sm);
}
.queue-info .material-icons {
    font-size: 1rem;
    color: var(--color-tertiary);
}
.queue-filters {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

/* Enhanced Table Sorting */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: var(--transition);
}
.sortable:hover {
    background: var(--color-hover);
}
.sort-indicator {
    display: inline-flex;
    align-items: center;
    margin-left: var(--spacing-xs);
    opacity: 0.5;
    transition: var(--transition);
}
.sortable:hover .sort-indicator {
    opacity: 1;
}
.sortable.sort-asc .sort-indicator .material-icons::before {
    content: 'keyboard_arrow_up';
}
.sortable.sort-desc .sort-indicator .material-icons::before {
    content: 'keyboard_arrow_down';
}
.sortable.sort-asc .sort-indicator,
.sortable.sort-desc .sort-indicator {
    opacity: 1;
    color: var(--color-primary);
}

/* Lock/Unlock Row Styles */
.locked-row {
    background: rgba(76, 175, 80, 0.05);
    border-left: 3px solid var(--color-success);
}
.unlocked-row {
    background: rgba(255, 152, 0, 0.03);
}
.lock-btn {
    background: var(--color-light);
    border: 1px solid var(--color-border);
}
.lock-btn.locked {
    background: rgba(76, 175, 80, 0.1);
    color: var(--color-success);
    border-color: var(--color-success);
}
.lock-btn.unlocked {
    background: rgba(255, 152, 0, 0.1);
    color: var(--color-warning);
    border-color: var(--color-warning);
}
.lock-btn:hover {
    transform: scale(1.05);
}

/* Enhanced Attachment Styles */
.attachment-cell {
    text-align: center;
}
.attachment-list {
    display: inline-block;
}
.attachment-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--color-light);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-xs) var(--spacing-sm);
    cursor: pointer;
    transition: var(--transition);
    font-size: var(--font-size-sm);
    color: var(--color-secondary);
}
.attachment-btn:hover:not(.disabled) {
    background: var(--color-primary);
    color: var(--color-white);
    border-color: var(--color-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}
.attachment-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
.attachment-count {
    font-weight: 600;
    font-size: var(--font-size-xs);
}

/* Attachment Modal Styles */
.attachment-modal {
    max-width: 600px;
    width: 90%;
}
.attachment-preview-list {
    display: grid;
    gap: var(--spacing-md);
}
.attachment-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    transition: var(--transition);
}
.attachment-item:hover {
    background: var(--color-hover);
    border-color: var(--color-primary);
}
.attachment-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    font-size: 1.5rem;
    color: var(--color-white);
    flex-shrink: 0;
}
.attachment-icon.pdf { background: #e53e3e; }
.attachment-icon.doc { background: #2b6cb0; }
.attachment-icon.excel { background: #38a169; }
.attachment-icon.image { background: #805ad5; }
.attachment-icon.archive { background: #d69e2e; }
.attachment-icon.cad { background: #319795; }
.attachment-icon.default { background: var(--color-tertiary); }
.attachment-info {
    flex: 1;
    min-width: 0;
}
.attachment-name {
    font-weight: 600;
    color: var(--color-primary);
    margin-bottom: var(--spacing-xs);
    word-break: break-word;
}
.attachment-meta {
    font-size: var(--font-size-sm);
    color: var(--color-tertiary);
    display: flex;
    gap: var(--spacing-md);
}
.attachment-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-shrink: 0;
}
.attachment-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    background: var(--color-white);
    color: var(--color-secondary);
    cursor: pointer;
    transition: var(--transition);
}
.attachment-action-btn:hover {
    background: var(--color-primary);
    color: var(--color-white);
    border-color: var(--color-primary);
}
.attachment-action-btn .material-icons {
    font-size: 1.1rem;
}

/* Purchase Order Edit Modal Styles */
.po-edit-overlay {
    z-index: 1100;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
}

.po-edit-modal {
    width: 90vw;
    height: 85vh;
    max-width: none;
    max-height: none;
    margin: 0;
    display: flex;
    flex-direction: column;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    animation: slideInModal 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInModal {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Fixed Header */
.po-edit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--color-white);
    border-bottom: 1px solid var(--color-border);
    box-shadow: var(--shadow-sm);
    flex-shrink: 0;
    z-index: 10;
}

.header-left .modal-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--color-primary);
    margin: 0 0 var(--spacing-xs) 0;
}

.breadcrumb-nav {
    font-size: var(--font-size-sm);
    color: var(--color-tertiary);
    font-weight: 500;
}

.navigation-controls {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--color-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-xs);
}

.nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: var(--border-radius-sm);
    background: transparent;
    color: var(--color-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.nav-btn:hover {
    background: var(--color-white);
    color: var(--color-primary);
    box-shadow: var(--shadow-sm);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.auto-save-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(76, 175, 80, 0.1);
    color: var(--color-success);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-right: var(--spacing-md);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.auto-save-indicator.visible {
    opacity: 1;
}

.auto-save-indicator .material-icons {
    font-size: 1rem;
}

/* Progress Timeline */
.progress-timeline-container {
    background: var(--color-white);
    border-bottom: 1px solid var(--color-border);
    padding: var(--spacing-lg) var(--spacing-xl);
    flex-shrink: 0;
}

.progress-timeline {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.progress-timeline::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--color-border);
    z-index: 1;
}

.timeline-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background: var(--color-white);
    padding: 0 var(--spacing-sm);
}

.step-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid var(--color-border);
    background: var(--color-white);
    margin-bottom: var(--spacing-sm);
    transition: var(--transition);
    cursor: pointer;
}

.step-circle.completed {
    background: var(--color-success);
    border-color: var(--color-success);
    color: var(--color-white);
}

.step-circle.in-progress {
    background: #2196F3;
    border-color: #2196F3;
    color: var(--color-white);
    animation: pulse 2s infinite;
}

.step-circle.rejected {
    background: var(--color-error);
    border-color: var(--color-error);
    color: var(--color-white);
}

.step-circle.pending {
    background: var(--color-light);
    border-color: var(--color-border);
    color: var(--color-tertiary);
}

.step-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--color-primary);
    color: var(--color-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.step-info {
    text-align: center;
    min-width: 100px;
}

.step-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--color-primary);
    margin-bottom: var(--spacing-xs);
}

.step-timestamp {
    font-size: var(--font-size-xs);
    color: var(--color-tertiary);
    margin-bottom: var(--spacing-xs);
}

.step-user {
    font-size: var(--font-size-xs);
    color: var(--color-secondary);
}

/* Modal Body */
.po-edit-body {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-xl);
    background: #fafafa;
}

.po-form {
    max-width: 1200px;
    margin: 0 auto;
}

/* Form Sections */
.form-section {
    background: var(--color-white);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: var(--transition);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--color-light);
    border-bottom: 1px solid var(--color-border);
    cursor: pointer;
    transition: var(--transition);
}

.section-header:hover {
    background: var(--color-hover);
}

.section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-primary);
    margin: 0;
}

.section-icon {
    width: 24px;
    height: 24px;
    color: var(--color-secondary);
}

.section-toggle {
    transition: transform 0.25s ease;
}

.section-toggle.collapsed {
    transform: rotate(-90deg);
}

.section-content {
    padding: var(--spacing-xl);
    transition: all 0.25s ease;
    overflow: hidden;
}

.section-content.collapsed {
    max-height: 0;
    padding: 0 var(--spacing-xl);
    opacity: 0;
}

/* Form Grid Layout */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    align-items: start;
}

.form-grid.two-column {
    grid-template-columns: repeat(2, 1fr);
}

.form-grid.three-column {
    grid-template-columns: repeat(3, 1fr);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--color-primary);
    margin-bottom: var(--spacing-xs);
}

.form-input {
    padding: var(--spacing-md);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    transition: var(--transition);
    background: var(--color-white);
}

.form-input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
    outline: none;
}

.form-input:invalid {
    border-color: var(--color-error);
}

.form-error {
    font-size: var(--font-size-xs);
    color: var(--color-error);
    margin-top: var(--spacing-xs);
}

/* Drop Zone Styles */
.drop-zone {
    position: relative;
    border: 2px dashed var(--color-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    transition: var(--transition);
    background: var(--color-white);
}

.drop-zone.drag-over {
    border-color: var(--color-primary);
    background: rgba(0, 0, 0, 0.02);
    transform: scale(1.02);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.drop-zone.has-value {
    border-style: solid;
    border-color: var(--color-success);
    background: rgba(76, 175, 80, 0.05);
}

.drop-zone-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-height: 48px;
}

.drop-zone-placeholder {
    color: var(--color-tertiary);
    font-style: italic;
    flex: 1;
}

.drop-zone-value {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--color-light);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-weight: 500;
    color: var(--color-primary);
}

.drop-zone-remove {
    background: none;
    border: none;
    color: var(--color-tertiary);
    cursor: pointer;
    padding: 2px;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.drop-zone-remove:hover {
    background: var(--color-error);
    color: var(--color-white);
}

/* Drag and Drop Panels */
.drag-drop-panels {
    position: fixed;
    top: 0;
    right: -320px;
    width: 300px;
    height: 100vh;
    background: var(--color-white);
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1200;
    transition: right 0.3s ease;
    overflow-y: auto;
    border-left: 1px solid var(--color-border);
}

.drag-drop-panels.visible {
    right: 0;
}

.drag-panel {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
}

.drag-panel h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--color-primary);
    margin: 0 0 var(--spacing-md) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.drag-chips {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.drag-chip {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--color-light);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    cursor: grab;
    transition: var(--transition);
    user-select: none;
}

.drag-chip:hover {
    background: var(--color-hover);
    border-color: var(--color-primary);
    transform: translateX(4px);
}

.drag-chip:active {
    cursor: grabbing;
}

.drag-chip.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.chip-icon {
    font-size: 1.1rem;
    color: var(--color-secondary);
}

/* Line Items Table */
.line-items-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-md);
}

.line-items-table th,
.line-items-table td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--color-border);
    font-size: var(--font-size-sm);
}

.line-items-table th {
    background: var(--color-light);
    font-weight: 600;
    color: var(--color-primary);
}

.line-items-table tbody tr:hover {
    background: var(--color-hover);
}

.add-line-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border: 2px dashed var(--color-border);
    border-radius: var(--border-radius);
    background: var(--color-light);
    color: var(--color-secondary);
    cursor: pointer;
    transition: var(--transition);
    margin-top: var(--spacing-md);
}

.add-line-item:hover {
    border-color: var(--color-primary);
    background: var(--color-hover);
    color: var(--color-primary);
}

/* Fixed Footer */
.po-edit-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--color-white);
    border-top: 1px solid var(--color-border);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.footer-left,
.footer-right {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

/* Enhanced Queue Management Styles */
.view-mode-selector {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--color-white);
    border-bottom: 1px solid var(--color-border);
    margin-bottom: var(--spacing-lg);
}

.view-modes {
    display: flex;
    gap: var(--spacing-xs);
    background: var(--color-light);
    border-radius: var(--border-radius);
    padding: var(--spacing-xs);
}

.view-mode-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-sm);
    background: transparent;
    color: var(--color-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.view-mode-btn:hover {
    background: var(--color-hover);
    color: var(--color-primary);
}

.view-mode-btn.active {
    background: var(--color-primary);
    color: var(--color-white);
    box-shadow: var(--shadow-sm);
}

.view-mode-btn .material-icons {
    font-size: 1.1rem;
}

.bulk-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

/* Queue Columns Container */
.queue-columns-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    min-height: 200px;
}

.queue-column {
    background: var(--color-white);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.queue-column:hover {
    box-shadow: var(--shadow-md);
}

.queue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--color-light);
    border-bottom: 1px solid var(--color-border);
}

.queue-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    color: var(--color-primary);
}

.queue-title .material-icons {
    font-size: 1.2rem;
}

.queue-count {
    background: var(--color-primary);
    color: var(--color-white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 8px;
    border-radius: var(--border-radius-lg);
    min-width: 24px;
    text-align: center;
}

.queue-actions-mini {
    display: flex;
    gap: var(--spacing-xs);
}

.queue-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    background: var(--color-white);
    color: var(--color-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.queue-action-btn:hover {
    background: var(--color-primary);
    color: var(--color-white);
    border-color: var(--color-primary);
}

.queue-action-btn .material-icons {
    font-size: 1rem;
}

/* Queue Drop Zones */
.queue-drop-zone {
    min-height: 150px;
    padding: var(--spacing-md);
    position: relative;
    transition: var(--transition);
}

.queue-drop-zone.drag-over {
    background: rgba(0, 0, 0, 0.02);
    border: 2px dashed var(--color-primary);
    border-radius: var(--border-radius);
}

.drop-zone-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    height: 120px;
    color: var(--color-tertiary);
    text-align: center;
    font-size: var(--font-size-sm);
    border: 2px dashed var(--color-border);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.drop-zone-placeholder .material-icons {
    font-size: 2rem;
    opacity: 0.5;
}

.queue-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.queue-item {
    background: var(--color-white);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
    cursor: grab;
    transition: var(--transition);
    user-select: none;
}

.queue-item:hover {
    box-shadow: var(--shadow-sm);
    transform: translateY(-1px);
}

.queue-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    cursor: grabbing;
}

.queue-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.queue-item-title {
    font-weight: 600;
    color: var(--color-primary);
    font-size: var(--font-size-sm);
}

.queue-item-status {
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
}

.queue-item-details {
    font-size: var(--font-size-xs);
    color: var(--color-secondary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Queue Filters Row */
.queue-filters-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--color-light);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
}

/* Multiple View Containers */
.data-views-container {
    position: relative;
}

.view-container {
    display: none;
    animation: fadeIn 0.3s ease;
}

.view-container.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Grid View Styles */
.grid-container {
    padding: var(--spacing-lg);
}

.po-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-lg);
}

.po-grid-item {
    background: var(--color-white);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    cursor: grab;
    user-select: none;
}

.po-grid-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.po-grid-item.dragging {
    opacity: 0.7;
    transform: rotate(3deg);
    cursor: grabbing;
}

.po-grid-item.selected {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

/* Card View Styles */
.cards-container {
    padding: var(--spacing-lg);
}

.po-cards {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.po-card-item {
    background: var(--color-white);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    cursor: grab;
    user-select: none;
}

.po-card-item:hover {
    box-shadow: var(--shadow-md);
}

.po-card-item.dragging {
    opacity: 0.7;
    cursor: grabbing;
}

.po-card-item.selected {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

/* List View Styles */
.list-container {
    padding: var(--spacing-lg);
}

.po-list {
    background: var(--color-white);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border);
    overflow: hidden;
}

.po-list-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
    transition: var(--transition);
    cursor: grab;
    user-select: none;
}

.po-list-item:last-child {
    border-bottom: none;
}

.po-list-item:hover {
    background: var(--color-hover);
}

.po-list-item.dragging {
    opacity: 0.7;
    cursor: grabbing;
}

.po-list-item.selected {
    background: rgba(0, 0, 0, 0.05);
    border-left: 3px solid var(--color-primary);
}

/* Compact View Styles */
.compact-container {
    padding: var(--spacing-md);
}

.po-compact {
    background: var(--color-white);
    border-radius: var(--border-radius);
    border: 1px solid var(--color-border);
    overflow: hidden;
}

.po-compact-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--color-border);
    transition: var(--transition);
    cursor: grab;
    user-select: none;
    font-size: var(--font-size-sm);
}

.po-compact-item:last-child {
    border-bottom: none;
}

.po-compact-item:hover {
    background: var(--color-hover);
}

.po-compact-item.dragging {
    opacity: 0.7;
    cursor: grabbing;
}

.po-compact-item.selected {
    background: rgba(0, 0, 0, 0.05);
    border-left: 2px solid var(--color-primary);
}

/* Responsive Design for Edit Modal */
@media (max-width: 1200px) {
    .po-edit-modal {
        width: 95vw;
        height: 90vh;
    }

    .form-grid.three-column {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .po-edit-modal {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
    }

    .po-edit-header {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }

    .header-left,
    .header-center,
    .header-right {
        width: 100%;
        justify-content: center;
    }

    .navigation-controls {
        justify-content: center;
    }

    .progress-timeline {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .progress-timeline::before {
        display: none;
    }

    .form-grid,
    .form-grid.two-column,
    .form-grid.three-column {
        grid-template-columns: 1fr;
    }

    .po-edit-footer {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }

    .footer-left,
    .footer-right {
        width: 100%;
        justify-content: center;
    }

    .drag-drop-panels {
        width: 100vw;
        right: -100vw;
    }
}

/* Responsive Design for New Components */
@media (max-width: 768px) {
    .search-input-wrapper {
        min-width: 200px;
    }
    .queue-tabs {
        flex-direction: column;
    }
    .queue-tab {
        border-bottom: 1px solid var(--color-border);
    }
    .queue-tab:last-child {
        border-bottom: none;
    }
    .queue-actions {
        flex-direction: column;
        align-items: stretch;
    }
    .queue-filters {
        justify-content: center;
    }
    .attachment-modal {
        width: 95%;
        margin: var(--spacing-md);
    }
    .attachment-item {
        flex-direction: column;
        text-align: center;
    }
    .attachment-meta {
        justify-content: center;
    }
}

    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <span class="material-icons">inventory_2</span>
                    <div class="logo-text">
                        <h1>Inventory Management System</h1>
                        <p>COMPLETE BUSINESS SOLUTION</p>
                    </div>
                </div>
            </div>

            <div class="header-center">
                <div class="search-container">
                    <span class="material-icons">search</span>
                    <input type="text" placeholder="Search inventory, orders, invoices..." class="search-input">
                    <span class="search-shortcut">Ctrl+K</span>
                </div>
            </div>

            <div class="header-right">
                <button class="icon-button" title="Notifications">
                    <span class="material-icons">notifications</span>
                </button>
                <button class="icon-button" title="Language">
                    <span class="material-icons">language</span>
                </button>
                <button class="icon-button" title="Theme">
                    <span class="material-icons">light_mode</span>
                </button>
                <button class="icon-button" title="Settings">
                    <span class="material-icons">settings</span>
                </button>
                <div class="user-profile">
                    <div class="user-avatar">JD</div>
                    <div class="user-info">
                        <span class="user-name">John Doe</span>
                        <span class="user-role">System Administrator</span>
                    </div>
                    <span class="material-icons">expand_more</span>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="navigation">
            <div class="nav-container">
                <a href="dashboard.html" class="nav-item" data-page="dashboard">
                    <span class="material-icons">dashboard</span>
                    <span>Dashboard</span>
                </a>
                <a href="purchase-order.html" class="nav-item active" data-page="purchase-order">
                    <span class="material-icons">shopping_cart</span>
                    <span>Purchase Order</span>
                </a>
                <a href="purchase-invoice.html" class="nav-item" data-page="purchase-invoice">
                    <span class="material-icons">receipt</span>
                    <span>Purchase Invoice</span>
                </a>
                <a href="purchase-grn.html" class="nav-item" data-page="purchase-grn">
                    <span class="material-icons">assignment</span>
                    <span>Purchase GRN</span>
                </a>
                <a href="gdr-settlement.html" class="nav-item" data-page="gdr-settlement">
                    <span class="material-icons">account_balance</span>
                    <span>GDR Settlement</span>
                </a>
                <a href="po-cancellation.html" class="nav-item" data-page="po-cancellation">
                    <span class="material-icons">cancel</span>
                    <span>PO Cancellation</span>
                </a>
                <a href="stock-transfer-request.html" class="nav-item" data-page="stock-transfer-request">
                    <span class="material-icons">swap_horiz</span>
                    <span>Stock Transfer Request</span>
                </a>
                <a href="stock-receipt-grn.html" class="nav-item" data-page="stock-receipt-grn">
                    <span class="material-icons">inventory</span>
                    <span>Stock Receipt GRN</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content" id="main-content">
            <!-- Hero Section -->
            <!-- <div class="hero-section">
                <div class="hero-content">
                    <h1 class="hero-title">Purchase Orders</h1>
                    <p class="hero-subtitle">Advanced order management with real-time insights and intelligent analytics</p>
                </div>
            </div> -->

            <!-- Innovative Metrics Grid -->
            <div class="metrics-grid">
                <div class="metric-card total-orders" data-metric="total">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons">shopping_cart</span>
                        </div>
                        <div class="metric-trend positive">
                            <span class="material-icons">trending_up</span>
                            <span>+12.5%</span>
                        </div>
                    </div>
                    <div class="metric-value" data-target="1234">0</div>
                    <div class="metric-label">Total Orders</div>
                    <div class="metric-progress">
                        <div class="metric-progress-bar" style="width: 0%" data-width="85%"></div>
                    </div>
                    <div class="metric-details">
                        <span>This Month</span>
                        <span>Target: 1,450</span>
                    </div>
                </div>

                <div class="metric-card pending" data-metric="pending">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons">schedule</span>
                        </div>
                        <div class="metric-trend negative">
                            <span class="material-icons">trending_down</span>
                            <span>-5.2%</span>
                        </div>
                    </div>
                    <div class="metric-value" data-target="89">0</div>
                    <div class="metric-label">Pending Approval</div>
                    <div class="metric-progress">
                        <div class="metric-progress-bar" style="width: 0%" data-width="35%"></div>
                    </div>
                    <div class="metric-details">
                        <span>Awaiting Review</span>
                        <span>Avg: 2.3 days</span>
                    </div>
                </div>

                <div class="metric-card approved" data-metric="approved">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons">check_circle</span>
                        </div>
                        <div class="metric-trend positive">
                            <span class="material-icons">trending_up</span>
                            <span>+8.1%</span>
                        </div>
                    </div>
                    <div class="metric-value" data-target="1145">0</div>
                    <div class="metric-label">Approved Orders</div>
                    <div class="metric-progress">
                        <div class="metric-progress-bar" style="width: 0%" data-width="92%"></div>
                    </div>
                    <div class="metric-details">
                        <span>Success Rate</span>
                        <span>92.8%</span>
                    </div>
                </div>
<div class="metric-card rejected" data-metric="rejected">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons">cancel</span>
                        </div>
                        <div class="metric-trend negative">
                            <span class="material-icons">trending_down</span>
                            <span>-2.1%</span>
                        </div>
                    </div>
                    <div class="metric-value" data-target="23">0</div>
                    <div class="metric-label">Rejected Orders</div>
                    <div class="metric-progress">
                        <div class="metric-progress-bar" style="width: 0%" data-width="15%"></div>
                    </div>
                    <div class="metric-details">
                        <span>This Month</span>
                        <span>Avg: 1.8%</span>
                    </div>
                </div>
                <div class="metric-card total-value" data-metric="value">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <span class="material-icons">attach_money</span>
                        </div>
                        <div class="metric-trend positive">
                            <span class="material-icons">trending_up</span>
                            <span>+15.3%</span>
                        </div>
                    </div>
                    <div class="metric-value" data-target="2400000" data-format="currency">$0</div>
                    <div class="metric-label">Total Value</div>
                    <div class="metric-progress">
                        <div class="metric-progress-bar" style="width: 0%" data-width="78%"></div>
                    </div>
                    <div class="metric-details">
                        <span>YTD Performance</span>
                        <span>Budget: $3.1M</span>
                    </div>
                </div>


            </div>

            <!-- Enhanced Page Actions -->
            <div class="page-actions">
                <div class="actions-left">
                    <button class="btn btn-primary btn-enhanced" id="new-po-btn">
                        <span class="material-icons">add</span>
                        New Purchase Order
                    </button>
                </div>

                <div class="actions-right">
                    <!-- Search Bar -->
                    <div class="search-bar-container">
                        <div class="search-input-wrapper">
                            <span class="material-icons search-icon">search</span>
                            <input type="text" id="table-search" class="search-input-table" placeholder="Search purchase orders...">
                            <button class="search-clear-btn" id="clear-search" style="display: none;">
                                <span class="material-icons">clear</span>
                            </button>
                        </div>
                    </div>
                    <button class="btn btn-secondary btn-enhanced" id="export-btn">
                        <span class="material-icons">file_download</span>
                        Export
                    </button>
                    <button class="btn btn-secondary btn-enhanced" id="refresh-btn">
                        <span class="material-icons">refresh</span>
                        Refresh
                    </button>
                </div>
            </div>

            <!-- Enhanced Queue Management System with Drag-and-Drop -->
            <div class="queue-management-container">
                <!-- View Mode Selector -->
                <div class="view-mode-selector">
                    <div class="view-modes">
                        <button class="view-mode-btn active" data-view="table" title="Table View">
                            <span class="material-icons">table_rows</span>
                            <span>Table</span>
                        </button>
                        <button class="view-mode-btn" data-view="grid" title="Grid View">
                            <span class="material-icons">grid_view</span>
                            <span>Grid</span>
                        </button>
                        <button class="view-mode-btn" data-view="card" title="Card View">
                            <span class="material-icons">view_agenda</span>
                            <span>Card</span>
                        </button>
                        <button class="view-mode-btn" data-view="list" title="List View">
                            <span class="material-icons">list</span>
                            <span>List</span>
                        </button>
                        <button class="view-mode-btn" data-view="compact" title="Compact View">
                            <span class="material-icons">view_compact</span>
                            <span>Compact</span>
                        </button>
                    </div>
                    <div class="bulk-actions">
                        <button class="btn btn-secondary" id="select-all-btn">
                            <span class="material-icons">select_all</span>
                            Select All
                        </button>
                        <button class="btn btn-secondary" id="bulk-move-btn" disabled>
                            <span class="material-icons">move_to_inbox</span>
                            Move Selected
                        </button>
                    </div>
                </div>

                <!-- Drag-and-Drop Queue Columns -->
                <div class="queue-columns-container" id="queue-columns">
                    <div class="queue-column" data-queue="my">
                        <div class="queue-header">
                            <div class="queue-title">
                                <span class="material-icons">person</span>
                                <span>My Queue</span>
                                <span class="queue-count" id="my-queue-count">0</span>
                            </div>
                            <div class="queue-actions-mini">
                                <button class="queue-action-btn" title="Clear All">
                                    <span class="material-icons">clear_all</span>
                                </button>
                            </div>
                        </div>
                        <div class="queue-drop-zone" data-queue="my">
                            <div class="drop-zone-placeholder">
                                <span class="material-icons">assignment_ind</span>
                                <span>Drop items here to assign to yourself</span>
                            </div>
                            <div class="queue-items" id="my-queue-items">
                                <!-- Items will be populated here -->
                            </div>
                        </div>
                    </div>

                    <div class="queue-column" data-queue="group">
                        <div class="queue-header">
                            <div class="queue-title">
                                <span class="material-icons">group</span>
                                <span>Group Queue</span>
                                <span class="queue-count" id="group-queue-count">0</span>
                            </div>
                            <div class="queue-actions-mini">
                                <button class="queue-action-btn" title="Assign Random">
                                    <span class="material-icons">shuffle</span>
                                </button>
                            </div>
                        </div>
                        <div class="queue-drop-zone" data-queue="group">
                            <div class="drop-zone-placeholder">
                                <span class="material-icons">groups</span>
                                <span>Drop items here for team assignment</span>
                            </div>
                            <div class="queue-items" id="group-queue-items">
                                <!-- Items will be populated here -->
                            </div>
                        </div>
                    </div>

                    <div class="queue-column" data-queue="all">
                        <div class="queue-header">
                            <div class="queue-title">
                                <span class="material-icons">public</span>
                                <span>All Queue</span>
                                <span class="queue-count" id="all-queue-count">0</span>
                            </div>
                            <div class="queue-actions-mini">
                                <button class="queue-action-btn" title="Auto-Assign">
                                    <span class="material-icons">auto_awesome</span>
                                </button>
                            </div>
                        </div>
                        <div class="queue-drop-zone" data-queue="all">
                            <div class="drop-zone-placeholder">
                                <span class="material-icons">inventory</span>
                                <span>Drop items here for general pool</span>
                            </div>
                            <div class="queue-items" id="all-queue-items">
                                <!-- Items will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Traditional Filters Row -->
                <div class="queue-filters-row">
                    <div class="queue-info">
                        <span class="material-icons">info</span>
                        <span id="queue-description">Drag and drop purchase orders between queues</span>
                    </div>
                    <div class="queue-filters">
                        <select class="filter-select" id="status-filter">
                            <option value="">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                            <option value="completed">Completed</option>
                        </select>
                        <select class="filter-select" id="priority-filter">
                            <option value="">All Priority</option>
                            <option value="urgent">Urgent</option>
                            <option value="high">High</option>
                            <option value="normal">Normal</option>
                            <option value="low">Low</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Multiple View Containers -->
            <div class="data-views-container">
                <!-- Table View (Default) -->
                <div class="view-container active" id="table-view">
                    <div class="table-container">
                        <div class="table-wrapper">
                    <table class="table" id="purchaseOrdersTable">
                        <thead>
                            <tr>
                                <th class="no-sort">Actions</th>
                                <th class="sortable" data-sort="po-number">
                                    PO Number
                                    <span class="sort-indicator">
                                        <span class="material-icons">unfold_more</span>
                                    </span>
                                </th>
                                <th class="sortable" data-sort="version">
                                    Ver.
                                    <span class="sort-indicator">
                                        <span class="material-icons">unfold_more</span>
                                    </span>
                                </th>
                                <th class="sortable" data-sort="fin-year">
                                    Fin.Year
                                    <span class="sort-indicator">
                                        <span class="material-icons">unfold_more</span>
                                    </span>
                                </th>
                                <th class="sortable" data-sort="date">
                                    Date
                                    <span class="sort-indicator">
                                        <span class="material-icons">unfold_more</span>
                                    </span>
                                </th>
                                <th class="sortable" data-sort="manufacturer">
                                    Manufacturer Name
                                    <span class="sort-indicator">
                                        <span class="material-icons">unfold_more</span>
                                    </span>
                                </th>
                                <th class="sortable" data-sort="type">
                                    Type of Purchase
                                    <span class="sort-indicator">
                                        <span class="material-icons">unfold_more</span>
                                    </span>
                                </th>
                                <th class="sortable" data-sort="class">
                                    Order Class
                                    <span class="sort-indicator">
                                        <span class="material-icons">unfold_more</span>
                                    </span>
                                </th>
                                <th class="sortable" data-sort="status">
                                    Status
                                    <span class="sort-indicator">
                                        <span class="material-icons">unfold_more</span>
                                    </span>
                                </th>
                                <th class="sortable" data-sort="attachments">
                                    Attachments
                                    <span class="sort-indicator">
                                        <span class="material-icons">unfold_more</span>
                                    </span>
                                </th>
                                <th class="sortable" data-sort="amount">
                                    Amount
                                    <span class="sort-indicator">
                                        <span class="material-icons">unfold_more</span>
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr data-queue="my" data-locked="true" class="locked-row">
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn lock-btn locked" title="Unlock Task" data-action="unlock">
                                            <span class="material-icons">lock</span>
                                        </button>
                                        <button class="action-btn view" title="View Details" data-action="view">
                                            <span class="material-icons">visibility</span>
                                        </button>
                                        <button class="action-btn edit" title="Edit PO" data-action="edit">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="action-btn" title="Download PO" data-action="download">
                                            <span class="material-icons">file_download</span>
                                        </button>
                                    </div>
                                </td>
                                <td><strong>PO-2024-001</strong></td>
                                <td>1.0</td>
                                <td>2024-2025</td>
                                <td data-date="2024-01-15">2024-01-15</td>
                                <td>ABC Suppliers Ltd.</td>
                                <td>Raw Materials</td>
                                <td>Standard</td>
                                <td><span class="status-badge success">Approved</span></td>
                                <td class="attachment-cell">
                                    <div class="attachment-list" data-attachments='[{"name":"PO_Contract.pdf","type":"pdf","size":"2.3MB"},{"name":"Specifications.docx","type":"doc","size":"1.1MB"}]'>
                                        <button class="attachment-btn" title="View Attachments">
                                            <span class="material-icons">attach_file</span>
                                            <span class="attachment-count">2</span>
                                        </button>
                                    </div>
                                </td>
                                <td><strong>$12,500.00</strong></td>
                            </tr>
                            <tr data-queue="group" data-locked="false" class="unlocked-row">
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn lock-btn unlocked" title="Lock Task" data-action="lock">
                                            <span class="material-icons">lock_open</span>
                                        </button>
                                        <button class="action-btn view" title="View Details" data-action="view">
                                            <span class="material-icons">visibility</span>
                                        </button>
                                        <button class="action-btn edit" title="Edit PO" data-action="edit">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="action-btn" title="Download PO" data-action="download">
                                            <span class="material-icons">file_download</span>
                                        </button>
                                    </div>
                                </td>
                                <td><strong>PO-2024-002</strong></td>
                                <td>1.1</td>
                                <td>2024-2025</td>
                                <td data-date="2024-01-14">2024-01-14</td>
                                <td>XYZ Trading Co.</td>
                                <td>Services</td>
                                <td>Urgent</td>
                                <td><span class="status-badge warning">Pending</span></td>
                                <td class="attachment-cell">
                                    <div class="attachment-list" data-attachments='[{"name":"Service_Agreement.pdf","type":"pdf","size":"1.8MB"}]'>
                                        <button class="attachment-btn" title="View Attachments">
                                            <span class="material-icons">attach_file</span>
                                            <span class="attachment-count">1</span>
                                        </button>
                                    </div>
                                </td>
                                <td><strong>$8,750.00</strong></td>
                            </tr>
                             <tr data-queue="all" data-locked="false" class="unlocked-row">
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn lock-btn unlocked" title="Lock Task" data-action="lock">
                                            <span class="material-icons">lock_open</span>
                                        </button>
                                        <button class="action-btn view" title="View Details" data-action="view">
                                            <span class="material-icons">visibility</span>
                                        </button>
                                        <button class="action-btn edit" title="Edit PO" data-action="edit">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="action-btn" title="Download PO" data-action="download">
                                            <span class="material-icons">file_download</span>
                                        </button>
                                    </div>
                                </td>
                                <td><strong>PO-2023-088</strong></td>
                                <td>2.0</td>
                                <td>2023-2024</td>
                                <td data-date="2023-12-20">2023-12-20</td>
                                <td>Global Tech Inc.</td>
                                <td>Capital Goods</td>
                                <td>Project-Based</td>
                                <td><span class="status-badge success">Approved</span></td>
                                <td class="attachment-cell">
                                    <div class="attachment-list" data-attachments='[{"name":"Technical_Specs.pdf","type":"pdf","size":"3.2MB"},{"name":"Drawings.dwg","type":"cad","size":"5.1MB"},{"name":"Contract.pdf","type":"pdf","size":"2.8MB"},{"name":"Compliance.xlsx","type":"excel","size":"1.5MB"},{"name":"Photos.zip","type":"archive","size":"12.3MB"}]'>
                                        <button class="attachment-btn" title="View Attachments">
                                            <span class="material-icons">attach_file</span>
                                            <span class="attachment-count">5</span>
                                        </button>
                                    </div>
                                </td>
                                <td><strong>$150,200.00</strong></td>
                            </tr>
                             <tr data-queue="my" data-locked="true" class="locked-row">
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn lock-btn locked" title="Unlock Task" data-action="unlock">
                                            <span class="material-icons">lock</span>
                                        </button>
                                        <button class="action-btn view" title="View Details" data-action="view">
                                            <span class="material-icons">visibility</span>
                                        </button>
                                        <button class="action-btn edit" title="Edit PO" data-action="edit">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="action-btn" title="Download PO" data-action="download">
                                            <span class="material-icons">file_download</span>
                                        </button>
                                    </div>
                                </td>
                                <td><strong>PO-2024-003</strong></td>
                                <td>1.0</td>
                                <td>2024-2025</td>
                                <td data-date="2024-01-20">2024-01-20</td>
                                <td>Office Supplies Co.</td>
                                <td>Consumables</td>
                                <td>Standard</td>
                                <td><span class="status-badge neutral">Completed</span></td>
                               <td class="attachment-cell">
                                    <div class="attachment-list" data-attachments='[]'>
                                        <button class="attachment-btn disabled" title="No Attachments">
                                            <span class="material-icons">attach_file</span>
                                            <span class="attachment-count">0</span>
                                        </button>
                                    </div>
                                </td>
                                <td><strong>$680.00</strong></td>
                            </tr>
                             <tr data-queue="group" data-locked="false" class="unlocked-row">
                                <td>
                                    <div class="action-buttons">
                                        <button class="action-btn lock-btn unlocked" title="Lock Task" data-action="lock">
                                            <span class="material-icons">lock_open</span>
                                        </button>
                                        <button class="action-btn view" title="View Details" data-action="view">
                                            <span class="material-icons">visibility</span>
                                        </button>
                                        <button class="action-btn edit" title="Edit PO" data-action="edit">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="action-btn" title="Download PO" data-action="download">
                                            <span class="material-icons">file_download</span>
                                        </button>
                                    </div>
                                </td>
                                <td><strong>PO-2024-004</strong></td>
                                <td>1.0</td>
                                <td>2024-2025</td>
                                <td data-date="2024-01-22">2024-01-22</td>
                                <td>Maintenance Solutions</td>
                                <td>Services</td>
                                <td>Recurring</td>
                                <td><span class="status-badge error">Rejected</span></td>
                                <td class="attachment-cell">
                                    <div class="attachment-list" data-attachments='[{"name":"Rejection_Notice.pdf","type":"pdf","size":"0.8MB"}]'>
                                        <button class="attachment-btn" title="View Attachments">
                                            <span class="material-icons">attach_file</span>
                                            <span class="attachment-count">1</span>
                                        </button>
                                    </div>
                                </td>
                                <td><strong>$1,200.00</strong></td>
                            </tr>
                            <tr id="no-results-row">
                                <td colspan="11">No purchase orders found matching your criteria.</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <div class="pagination-info">
                        Showing 1-5 of 1,234 POs
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" disabled title="Previous Page"><span class="material-icons">chevron_left</span></button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn">...</button>
                        <button class="pagination-btn">309</button>
                        <button class="pagination-btn" title="Next Page"><span class="material-icons">chevron_right</span></button>
                    </div>
                </div>
                    </div>
                </div>

                <!-- Grid View -->
                <div class="view-container" id="grid-view">
                    <div class="grid-container">
                        <div class="po-grid" id="po-grid">
                            <!-- Grid items will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Card View -->
                <div class="view-container" id="card-view">
                    <div class="cards-container">
                        <div class="po-cards" id="po-cards">
                            <!-- Card items will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- List View -->
                <div class="view-container" id="list-view">
                    <div class="list-container">
                        <div class="po-list" id="po-list">
                            <!-- List items will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Compact View -->
                <div class="view-container" id="compact-view">
                    <div class="compact-container">
                        <div class="po-compact" id="po-compact">
                            <!-- Compact items will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Attachment Preview Modal -->
    <div id="attachment-modal" class="modal-overlay">
        <div class="modal attachment-modal">
            <div class="modal-header">
                <h3 class="modal-title">Attachments</h3>
                <button class="modal-close" onclick="closeAttachmentModal()">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="attachment-list" class="attachment-preview-list">
                    <!-- Attachment items will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Purchase Order Edit Modal -->
    <div id="po-edit-modal" class="modal-overlay po-edit-overlay">
        <div class="modal po-edit-modal">
            <!-- Fixed Header with Navigation -->
            <div class="po-edit-header">
                <div class="header-left">
                    <h2 class="modal-title">Edit Purchase Order</h2>
                    <div class="breadcrumb-nav">
                        <span id="record-position">Record 1 of 156</span>
                    </div>
                </div>
                <div class="header-center">
                    <div class="navigation-controls">
                        <button class="nav-btn" id="first-record" title="First Record (Ctrl+Home)">
                            <span class="material-icons">first_page</span>
                        </button>
                        <button class="nav-btn" id="prev-record" title="Previous Record (Ctrl+Left)">
                            <span class="material-icons">chevron_left</span>
                        </button>
                        <button class="nav-btn" id="next-record" title="Next Record (Ctrl+Right)">
                            <span class="material-icons">chevron_right</span>
                        </button>
                        <button class="nav-btn" id="last-record" title="Last Record (Ctrl+End)">
                            <span class="material-icons">last_page</span>
                        </button>
                    </div>
                </div>
                <div class="header-right">
                    <div class="auto-save-indicator" id="auto-save-status">
                        <span class="material-icons">cloud_done</span>
                        <span>Draft Saved</span>
                    </div>
                    <button class="modal-close" onclick="closePOEditModal()">
                        <span class="material-icons">close</span>
                    </button>
                </div>
            </div>

            <!-- Progress Timeline -->
            <div class="progress-timeline-container">
                <div class="progress-timeline" id="progress-timeline">
                    <!-- Timeline will be populated dynamically -->
                </div>
            </div>

            <!-- Modal Body with Form -->
            <div class="po-edit-body">
                <form id="po-edit-form" class="po-form">
                    <!-- Form sections will be populated dynamically -->
                </form>
            </div>

            <!-- Fixed Footer with Actions -->
            <div class="po-edit-footer">
                <div class="footer-left">
                    <button type="button" class="btn btn-secondary" id="print-export-btn">
                        <span class="material-icons">print</span>
                        Print/Export
                    </button>
                </div>
                <div class="footer-right">
                    <button type="button" class="btn btn-secondary" id="save-draft-btn">
                        <span class="material-icons">save</span>
                        Save Draft
                    </button>
                    <button type="button" class="btn btn-primary" id="save-close-btn">
                        <span class="material-icons">check</span>
                        Save & Close
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closePOEditModal()">
                        <span class="material-icons">cancel</span>
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Drag and Drop Side Panels -->
    <div id="drag-drop-panels" class="drag-drop-panels">
        <!-- Status Options Panel -->
        <div class="drag-panel" id="status-panel">
            <h4>Status Options</h4>
            <div class="drag-chips">
                <div class="drag-chip" data-value="draft" draggable="true">
                    <span class="chip-icon material-icons">edit</span>
                    <span>Draft</span>
                </div>
                <div class="drag-chip" data-value="pending" draggable="true">
                    <span class="chip-icon material-icons">schedule</span>
                    <span>Pending</span>
                </div>
                <div class="drag-chip" data-value="approved" draggable="true">
                    <span class="chip-icon material-icons">check_circle</span>
                    <span>Approved</span>
                </div>
                <div class="drag-chip" data-value="rejected" draggable="true">
                    <span class="chip-icon material-icons">cancel</span>
                    <span>Rejected</span>
                </div>
                <div class="drag-chip" data-value="completed" draggable="true">
                    <span class="chip-icon material-icons">done_all</span>
                    <span>Completed</span>
                </div>
            </div>
        </div>

        <!-- Purchase Type Panel -->
        <div class="drag-panel" id="type-panel">
            <h4>Purchase Types</h4>
            <div class="drag-chips">
                <div class="drag-chip" data-value="raw-materials" draggable="true">
                    <span class="chip-icon material-icons">inventory</span>
                    <span>Raw Materials</span>
                </div>
                <div class="drag-chip" data-value="services" draggable="true">
                    <span class="chip-icon material-icons">handyman</span>
                    <span>Services</span>
                </div>
                <div class="drag-chip" data-value="capital-goods" draggable="true">
                    <span class="chip-icon material-icons">precision_manufacturing</span>
                    <span>Capital Goods</span>
                </div>
                <div class="drag-chip" data-value="consumables" draggable="true">
                    <span class="chip-icon material-icons">shopping_cart</span>
                    <span>Consumables</span>
                </div>
            </div>
        </div>

        <!-- Order Class Panel -->
        <div class="drag-panel" id="class-panel">
            <h4>Order Classes</h4>
            <div class="drag-chips">
                <div class="drag-chip" data-value="standard" draggable="true">
                    <span class="chip-icon material-icons">assignment</span>
                    <span>Standard</span>
                </div>
                <div class="drag-chip" data-value="urgent" draggable="true">
                    <span class="chip-icon material-icons">priority_high</span>
                    <span>Urgent</span>
                </div>
                <div class="drag-chip" data-value="project-based" draggable="true">
                    <span class="chip-icon material-icons">account_tree</span>
                    <span>Project-Based</span>
                </div>
                <div class="drag-chip" data-value="recurring" draggable="true">
                    <span class="chip-icon material-icons">repeat</span>
                    <span>Recurring</span>
                </div>
            </div>
        </div>

        <!-- Vendors Panel -->
        <div class="drag-panel" id="vendors-panel">
            <h4>Vendors</h4>
            <div class="drag-chips">
                <div class="drag-chip" data-value="abc-suppliers" draggable="true">
                    <span class="chip-icon material-icons">business</span>
                    <span>ABC Suppliers Ltd.</span>
                </div>
                <div class="drag-chip" data-value="xyz-trading" draggable="true">
                    <span class="chip-icon material-icons">store</span>
                    <span>XYZ Trading Co.</span>
                </div>
                <div class="drag-chip" data-value="global-tech" draggable="true">
                    <span class="chip-icon material-icons">computer</span>
                    <span>Global Tech Inc.</span>
                </div>
                <div class="drag-chip" data-value="office-supplies" draggable="true">
                    <span class="chip-icon material-icons">local_office</span>
                    <span>Office Supplies Co.</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Close modal when clicking outside
        document.getElementById('attachment-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAttachmentModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeAttachmentModal();
            }
        });
    </script>

    <!-- Scripts -->
    <script src="../scripts/main.js"></script>
    <script src="../scripts/navigation.js"></script>
    <script src="../scripts/components.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize unified design system
            if (window.App) {
                window.App.init();
            }

            // Set current page for navigation
            if (window.Navigation) {
                window.Navigation.currentPage = 'purchase-order';
            }

            // Initialize purchase order specific features
            initializePurchaseOrderPage();
        });

        function initializePurchaseOrderPage() {
            // Initialize metric animations
            initializeMetricAnimations();

            // Initialize metric card interactions
            initializeMetricCardInteractions();

            // Initialize search functionality
            initializeSearchFunctionality();

            // Initialize queue management
            initializeQueueManagement();

            // Initialize enhanced queue management with drag-and-drop
            initializeEnhancedQueueManagement();

            // Initialize view mode switching
            initializeViewModes();

            // Initialize enhanced table sorting
            initializeEnhancedSorting();

            // Initialize attachment functionality
            initializeAttachmentFunctionality();

            // Initialize lock/unlock functionality
            initializeLockUnlockFunctionality();

            // New Purchase Order button
            document.getElementById('new-po-btn').addEventListener('click', function() {
                if (window.Components) {
                    const modal = window.Components.createModal(
                        'Create New Purchase Order',
                        getPurchaseOrderForm(),
                        {
                            width: '800px',
                            footer: `
                                <button class="btn btn-secondary" onclick="window.Components.closeModal(this.closest('.modal-backdrop'))">Cancel</button>
                                <button class="btn btn-primary" onclick="savePurchaseOrder()">Create Purchase Order</button>
                            `
                        }
                    );
                }
            });

            // Export button
            document.getElementById('export-btn').addEventListener('click', function() {
                exportTableData();
            });

            // Refresh button
            document.getElementById('refresh-btn').addEventListener('click', function() {
                refreshMetrics();
                refreshTableData();
            });

            // Action buttons
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', handleTableAction);
            });

            // Filter functionality
            document.querySelectorAll('.filter-select').forEach(select => {
                select.addEventListener('change', applyFilters);
            });
        }

        // Initialize metric animations
        function initializeMetricAnimations() {
            // Animate metric values
            setTimeout(() => {
                document.querySelectorAll('.metric-value').forEach(element => {
                    const target = parseInt(element.getAttribute('data-target'));
                    const format = element.getAttribute('data-format');
                    animateValue(element, target, format);
                });

                // Animate progress bars
                document.querySelectorAll('.metric-progress-bar').forEach(bar => {
                    const targetWidth = bar.getAttribute('data-width');
                    setTimeout(() => {
                        bar.style.width = targetWidth;
                    }, 500);
                });
            }, 600);
        }

        // Initialize search functionality
        function initializeSearchFunctionality() {
            const searchInput = document.getElementById('table-search');
            const clearBtn = document.getElementById('clear-search');

            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                if (query) {
                    clearBtn.style.display = 'block';
                    filterTableRows(query);
                } else {
                    clearBtn.style.display = 'none';
                    showAllRows();
                }
            });

            clearBtn.addEventListener('click', function() {
                searchInput.value = '';
                this.style.display = 'none';
                showAllRows();
                searchInput.focus();
            });
        }

        // Filter table rows based on search query
        function filterTableRows(query) {
            const table = document.getElementById('purchaseOrdersTable');
            const rows = table.querySelectorAll('tbody tr:not(#no-results-row)');
            let visibleCount = 0;

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const matches = text.includes(query.toLowerCase());

                if (matches) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // Show/hide no results message
            const noResultsRow = document.getElementById('no-results-row');
            if (visibleCount === 0) {
                noResultsRow.style.display = '';
            } else {
                noResultsRow.style.display = 'none';
            }
        }

        // Show all table rows
        function showAllRows() {
            const table = document.getElementById('purchaseOrdersTable');
            const rows = table.querySelectorAll('tbody tr:not(#no-results-row)');

            rows.forEach(row => {
                row.style.display = '';
            });

            document.getElementById('no-results-row').style.display = 'none';
        }

        // Initialize queue management
        function initializeQueueManagement() {
            const queueTabs = document.querySelectorAll('.queue-tab');
            const queueDescription = document.getElementById('queue-description');

            queueTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const queueType = this.getAttribute('data-queue');
                    switchQueue(queueType);

                    // Update active tab
                    queueTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // Update description
                    updateQueueDescription(queueType);
                });
            });
        }

        // Switch queue view
        function switchQueue(queueType) {
            const table = document.getElementById('purchaseOrdersTable');
            const rows = table.querySelectorAll('tbody tr:not(#no-results-row)');

            rows.forEach(row => {
                const rowQueue = row.getAttribute('data-queue');
                if (queueType === 'all' || rowQueue === queueType) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            updateQueueCounts();
        }

        // Update queue description
        function updateQueueDescription(queueType) {
            const descriptions = {
                'my': 'Showing tasks assigned to you',
                'group': 'Showing tasks assigned to your team',
                'all': 'Showing all tasks from all departments'
            };

            document.getElementById('queue-description').textContent = descriptions[queueType];
        }

        // Update queue counts
        function updateQueueCounts() {
            const table = document.getElementById('purchaseOrdersTable');
            const allRows = table.querySelectorAll('tbody tr:not(#no-results-row)');

            const counts = {
                my: 0,
                group: 0,
                all: allRows.length
            };

            allRows.forEach(row => {
                const queue = row.getAttribute('data-queue');
                if (counts[queue] !== undefined) {
                    counts[queue]++;
                }
            });

            // Update count displays for both old and new queue systems
            document.querySelectorAll('.queue-tab').forEach(tab => {
                const queueType = tab.getAttribute('data-queue');
                const countElement = tab.querySelector('.queue-count');
                if (countElement && counts[queueType] !== undefined) {
                    countElement.textContent = counts[queueType];
                }
            });

            // Update new queue column counts
            document.getElementById('my-queue-count').textContent = counts.my;
            document.getElementById('group-queue-count').textContent = counts.group;
            document.getElementById('all-queue-count').textContent = counts.all;
        }

        // Initialize Enhanced Queue Management with Drag-and-Drop
        function initializeEnhancedQueueManagement() {
            // Initialize drag and drop for queue columns
            initializeQueueDragAndDrop();

            // Initialize bulk actions
            initializeBulkActions();

            // Populate queue columns with initial data
            populateQueueColumns();

            // Update initial counts
            updateQueueCounts();
        }

        // Initialize Queue Drag and Drop
        function initializeQueueDragAndDrop() {
            const queueDropZones = document.querySelectorAll('.queue-drop-zone');

            queueDropZones.forEach(zone => {
                zone.addEventListener('dragover', handleQueueDragOver);
                zone.addEventListener('dragleave', handleQueueDragLeave);
                zone.addEventListener('drop', handleQueueDrop);
            });
        }

        // Handle drag over queue zones
        function handleQueueDragOver(e) {
            e.preventDefault();
            this.classList.add('drag-over');
        }

        // Handle drag leave queue zones
        function handleQueueDragLeave(e) {
            this.classList.remove('drag-over');
        }

        // Handle drop on queue zones
        function handleQueueDrop(e) {
            e.preventDefault();
            this.classList.remove('drag-over');

            const targetQueue = this.getAttribute('data-queue');
            const draggedData = e.dataTransfer.getData('application/json');

            if (draggedData) {
                const poData = JSON.parse(draggedData);
                moveToQueue(poData.poNumber, targetQueue);

                if (window.Components) {
                    window.Components.showToast(`Moved ${poData.poNumber} to ${targetQueue} queue`, 'success');
                }
            }
        }

        // Move PO to different queue
        function moveToQueue(poNumber, targetQueue) {
            const table = document.getElementById('purchaseOrdersTable');
            const rows = table.querySelectorAll('tbody tr:not(#no-results-row)');

            rows.forEach(row => {
                const rowPONumber = row.querySelector('td:nth-child(2) strong').textContent;
                if (rowPONumber === poNumber) {
                    row.setAttribute('data-queue', targetQueue);

                    // Update lock status based on queue
                    const lockBtn = row.querySelector('.lock-btn');
                    if (lockBtn) {
                        if (targetQueue === 'my') {
                            lockBtn.classList.remove('unlocked');
                            lockBtn.classList.add('locked');
                            lockBtn.title = 'Unlock Task';
                            lockBtn.querySelector('.material-icons').textContent = 'lock';
                            row.classList.remove('unlocked-row');
                            row.classList.add('locked-row');
                            row.setAttribute('data-locked', 'true');
                        } else {
                            lockBtn.classList.remove('locked');
                            lockBtn.classList.add('unlocked');
                            lockBtn.title = 'Lock Task';
                            lockBtn.querySelector('.material-icons').textContent = 'lock_open';
                            row.classList.remove('locked-row');
                            row.classList.add('unlocked-row');
                            row.setAttribute('data-locked', 'false');
                        }
                    }
                }
            });

            updateQueueCounts();
            populateQueueColumns();
            refreshCurrentView();
        }

        // Populate Queue Columns
        function populateQueueColumns() {
            const table = document.getElementById('purchaseOrdersTable');
            const rows = table.querySelectorAll('tbody tr:not(#no-results-row)');

            // Clear existing queue items
            document.getElementById('my-queue-items').innerHTML = '';
            document.getElementById('group-queue-items').innerHTML = '';
            document.getElementById('all-queue-items').innerHTML = '';

            rows.forEach(row => {
                const queue = row.getAttribute('data-queue');
                const poNumber = row.querySelector('td:nth-child(2) strong').textContent;
                const status = row.querySelector('.status-badge').textContent;
                const amount = row.children[10].textContent;
                const manufacturer = row.children[5].textContent;

                const queueItem = createQueueItem({
                    poNumber,
                    status,
                    amount,
                    manufacturer,
                    queue
                });

                const targetContainer = document.getElementById(`${queue}-queue-items`);
                if (targetContainer) {
                    targetContainer.appendChild(queueItem);
                }
            });
        }

        // Create Queue Item Element
        function createQueueItem(data) {
            const item = document.createElement('div');
            item.className = 'queue-item';
            item.draggable = true;
            item.setAttribute('data-po-number', data.poNumber);

            item.innerHTML = `
                <div class="queue-item-header">
                    <div class="queue-item-title">${data.poNumber}</div>
                    <div class="queue-item-status status-badge ${getStatusClass(data.status)}">${data.status}</div>
                </div>
                <div class="queue-item-details">
                    <span>${data.manufacturer}</span>
                    <span><strong>${data.amount}</strong></span>
                </div>
            `;

            // Add drag event listeners
            item.addEventListener('dragstart', (e) => {
                item.classList.add('dragging');
                e.dataTransfer.setData('application/json', JSON.stringify(data));
            });

            item.addEventListener('dragend', () => {
                item.classList.remove('dragging');
            });

            return item;
        }

        // Get status class for styling
        function getStatusClass(status) {
            const statusMap = {
                'Pending': 'warning',
                'Approved': 'success',
                'Rejected': 'error',
                'Completed': 'neutral'
            };
            return statusMap[status] || 'neutral';
        }

        // Initialize Bulk Actions
        function initializeBulkActions() {
            const selectAllBtn = document.getElementById('select-all-btn');
            const bulkMoveBtn = document.getElementById('bulk-move-btn');

            selectAllBtn.addEventListener('click', toggleSelectAll);
            bulkMoveBtn.addEventListener('click', showBulkMoveOptions);
        }

        // Toggle select all items
        function toggleSelectAll() {
            const currentView = document.querySelector('.view-container.active');
            const items = currentView.querySelectorAll('[data-po-number]');
            const selectAllBtn = document.getElementById('select-all-btn');
            const bulkMoveBtn = document.getElementById('bulk-move-btn');

            const allSelected = Array.from(items).every(item => item.classList.contains('selected'));

            items.forEach(item => {
                if (allSelected) {
                    item.classList.remove('selected');
                } else {
                    item.classList.add('selected');
                }
            });

            const selectedCount = currentView.querySelectorAll('.selected').length;
            bulkMoveBtn.disabled = selectedCount === 0;
            bulkMoveBtn.innerHTML = `
                <span class="material-icons">move_to_inbox</span>
                Move Selected (${selectedCount})
            `;

            selectAllBtn.innerHTML = `
                <span class="material-icons">${allSelected ? 'deselect' : 'select_all'}</span>
                ${allSelected ? 'Deselect All' : 'Select All'}
            `;
        }

        // Show bulk move options
        function showBulkMoveOptions() {
            if (window.Components) {
                const modal = window.Components.createModal(
                    'Bulk Move Purchase Orders',
                    `
                        <div style="padding: 1rem;">
                            <p>Select target queue for selected purchase orders:</p>
                            <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                                <button class="btn btn-secondary" onclick="bulkMoveToQueue('my')">
                                    <span class="material-icons">person</span>
                                    My Queue
                                </button>
                                <button class="btn btn-secondary" onclick="bulkMoveToQueue('group')">
                                    <span class="material-icons">group</span>
                                    Group Queue
                                </button>
                                <button class="btn btn-secondary" onclick="bulkMoveToQueue('all')">
                                    <span class="material-icons">public</span>
                                    All Queue
                                </button>
                            </div>
                        </div>
                    `,
                    { width: '400px' }
                );
            }
        }

        // Bulk move to queue
        function bulkMoveToQueue(targetQueue) {
            const currentView = document.querySelector('.view-container.active');
            const selectedItems = currentView.querySelectorAll('.selected');

            selectedItems.forEach(item => {
                const poNumber = item.getAttribute('data-po-number');
                moveToQueue(poNumber, targetQueue);
                item.classList.remove('selected');
            });

            if (window.Components) {
                window.Components.showToast(`Moved ${selectedItems.length} items to ${targetQueue} queue`, 'success');
                window.Components.closeModal(document.querySelector('.modal-backdrop'));
            }

            // Reset bulk actions
            document.getElementById('bulk-move-btn').disabled = true;
            document.getElementById('bulk-move-btn').innerHTML = `
                <span class="material-icons">move_to_inbox</span>
                Move Selected
            `;
        }

        // Initialize attachment functionality
        function initializeAttachmentFunctionality() {
            document.querySelectorAll('.attachment-btn').forEach(btn => {
                if (!btn.classList.contains('disabled')) {
                    btn.addEventListener('click', function() {
                        const attachmentList = this.closest('.attachment-list');
                        const attachmentsData = JSON.parse(attachmentList.getAttribute('data-attachments'));
                        showAttachmentModal(attachmentsData);
                    });
                }
            });
        }

        // Show attachment modal
        function showAttachmentModal(attachments) {
            const modal = document.getElementById('attachment-modal');
            const attachmentList = document.getElementById('attachment-list');

            // Clear existing content
            attachmentList.innerHTML = '';

            if (attachments.length === 0) {
                attachmentList.innerHTML = '<p style="text-align: center; color: var(--color-tertiary); padding: var(--spacing-xl);">No attachments available</p>';
            } else {
                attachments.forEach(attachment => {
                    const attachmentItem = createAttachmentItem(attachment);
                    attachmentList.appendChild(attachmentItem);
                });
            }

            modal.classList.add('active');
        }

        // Create attachment item element
        function createAttachmentItem(attachment) {
            const item = document.createElement('div');
            item.className = 'attachment-item';

            const iconClass = getFileIconClass(attachment.type);
            const iconName = getFileIconName(attachment.type);

            item.innerHTML = `
                <div class="attachment-icon ${iconClass}">
                    <span class="material-icons">${iconName}</span>
                </div>
                <div class="attachment-info">
                    <div class="attachment-name">${attachment.name}</div>
                    <div class="attachment-meta">
                        <span>Type: ${attachment.type.toUpperCase()}</span>
                        <span>Size: ${attachment.size}</span>
                    </div>
                </div>
                <div class="attachment-actions">
                    <button class="attachment-action-btn" title="Preview" onclick="previewAttachment('${attachment.name}')">
                        <span class="material-icons">visibility</span>
                    </button>
                    <button class="attachment-action-btn" title="Download" onclick="downloadAttachment('${attachment.name}')">
                        <span class="material-icons">file_download</span>
                    </button>
                </div>
            `;

            return item;
        }

        // Get file icon class based on type
        function getFileIconClass(type) {
            const iconMap = {
                'pdf': 'pdf',
                'doc': 'doc',
                'docx': 'doc',
                'excel': 'excel',
                'xlsx': 'excel',
                'xls': 'excel',
                'jpg': 'image',
                'jpeg': 'image',
                'png': 'image',
                'gif': 'image',
                'zip': 'archive',
                'rar': 'archive',
                'dwg': 'cad',
                'dxf': 'cad'
            };

            return iconMap[type.toLowerCase()] || 'default';
        }

        // Get file icon name
        function getFileIconName(type) {
            const iconMap = {
                'pdf': 'picture_as_pdf',
                'doc': 'description',
                'docx': 'description',
                'excel': 'table_chart',
                'xlsx': 'table_chart',
                'xls': 'table_chart',
                'jpg': 'image',
                'jpeg': 'image',
                'png': 'image',
                'gif': 'image',
                'zip': 'archive',
                'rar': 'archive',
                'dwg': 'architecture',
                'dxf': 'architecture'
            };

            return iconMap[type.toLowerCase()] || 'insert_drive_file';
        }

        // Close attachment modal
        function closeAttachmentModal() {
            document.getElementById('attachment-modal').classList.remove('active');
        }

        // Preview attachment (placeholder)
        function previewAttachment(filename) {
            if (window.Components) {
                window.Components.showToast(`Preview for ${filename} - Feature coming soon!`, 'info');
            }
        }

        // Download attachment (placeholder)
        function downloadAttachment(filename) {
            if (window.Components) {
                window.Components.showToast(`Downloading ${filename}...`, 'success');
            }
        }

        // Initialize lock/unlock functionality
        function initializeLockUnlockFunctionality() {
            document.querySelectorAll('.lock-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const row = this.closest('tr');
                    const isLocked = row.getAttribute('data-locked') === 'true';

                    if (isLocked) {
                        unlockTask(row, this);
                    } else {
                        lockTask(row, this);
                    }
                });
            });
        }

        // Lock task
        function lockTask(row, button) {
            row.setAttribute('data-locked', 'true');
            row.setAttribute('data-queue', 'my');
            row.classList.remove('unlocked-row');
            row.classList.add('locked-row');

            button.classList.remove('unlocked');
            button.classList.add('locked');
            button.title = 'Unlock Task';
            button.querySelector('.material-icons').textContent = 'lock';

            updateQueueCounts();

            if (window.Components) {
                window.Components.showToast('Task locked and moved to your queue', 'success');
            }
        }

        // Unlock task
        function unlockTask(row, button) {
            row.setAttribute('data-locked', 'false');
            row.setAttribute('data-queue', 'group');
            row.classList.remove('locked-row');
            row.classList.add('unlocked-row');

            button.classList.remove('locked');
            button.classList.add('unlocked');
            button.title = 'Lock Task';
            button.querySelector('.material-icons').textContent = 'lock_open';

            updateQueueCounts();

            if (window.Components) {
                window.Components.showToast('Task unlocked and returned to group queue', 'info');
            }
        }

        // Initialize enhanced sorting
        function initializeEnhancedSorting() {
            document.querySelectorAll('.sortable').forEach(header => {
                header.addEventListener('click', function() {
                    const table = this.closest('table');
                    const tbody = table.querySelector('tbody');
                    const rows = Array.from(tbody.querySelectorAll('tr:not(#no-results-row)'));
                    const columnIndex = Array.from(this.parentNode.children).indexOf(this);
                    const isAscending = this.classList.contains('sort-asc');

                    // Remove existing sort classes from all headers
                    this.parentNode.querySelectorAll('.sortable').forEach(th => {
                        th.classList.remove('sort-asc', 'sort-desc');
                        const indicator = th.querySelector('.sort-indicator .material-icons');
                        indicator.textContent = 'unfold_more';
                    });

                    // Add new sort class
                    this.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
                    const indicator = this.querySelector('.sort-indicator .material-icons');
                    indicator.textContent = isAscending ? 'keyboard_arrow_down' : 'keyboard_arrow_up';

                    // Sort rows
                    rows.sort((a, b) => {
                        const aText = a.children[columnIndex].textContent.trim();
                        const bText = b.children[columnIndex].textContent.trim();

                        // Try to parse as numbers for amount column
                        if (this.getAttribute('data-sort') === 'amount') {
                            const aNum = parseFloat(aText.replace(/[^0-9.-]/g, ''));
                            const bNum = parseFloat(bText.replace(/[^0-9.-]/g, ''));
                            return isAscending ? bNum - aNum : aNum - bNum;
                        }

                        // Try to parse as dates
                        if (this.getAttribute('data-sort') === 'date') {
                            const aDate = new Date(aText);
                            const bDate = new Date(bText);
                            return isAscending ? bDate - aDate : aDate - bDate;
                        }

                        // Default string comparison
                        return isAscending ? bText.localeCompare(aText) : aText.localeCompare(bText);
                    });

                    // Reorder rows in DOM
                    rows.forEach(row => tbody.appendChild(row));

                    // Add sort animation
                    rows.forEach((row, index) => {
                        row.style.animation = `fadeInScale 0.3s ease-out ${index * 0.02}s both`;
                    });
                });
            });
        }

        // Export table data
        function exportTableData() {
            if (window.Components) {
                window.Components.showToast('Exporting purchase order data...', 'info');
                // Simulate export process
                setTimeout(() => {
                    window.Components.showToast('Export completed successfully!', 'success');
                }, 2000);
            }
        }

        // Refresh table data
        function refreshTableData() {
            if (window.Components) {
                window.Components.showToast('Refreshing purchase order data...', 'info');
                // Simulate refresh process
                setTimeout(() => {
                    updateQueueCounts();
                    window.Components.showToast('Data refreshed successfully!', 'success');
                }, 1500);
            }
        }

        // Handle table actions
        function handleTableAction(e) {
            e.preventDefault();
            e.stopPropagation();

            const button = e.currentTarget;
            const action = button.getAttribute('data-action');
            const row = button.closest('tr');

            if (!row || !action) {
                console.error('Could not determine action or row');
                return;
            }

            const poNumberElement = row.querySelector('td:nth-child(2) strong');
            if (!poNumberElement) {
                console.error('Could not find PO number element');
                return;
            }

            const poNumber = poNumberElement.textContent;

            switch (action) {
                case 'view':
                    viewPurchaseOrder(poNumber);
                    break;
                case 'edit':
                    editPurchaseOrder(poNumber);
                    break;
                case 'download':
                    downloadPurchaseOrder(poNumber);
                    break;
                case 'lock':
                    // Handled by lock/unlock functionality
                    break;
                case 'unlock':
                    // Handled by lock/unlock functionality
                    break;
            }
        }

        // View purchase order
        function viewPurchaseOrder(poNumber) {
            if (window.Components) {
                window.Components.showToast(`Viewing details for ${poNumber}`, 'info');
            }
        }

        // Edit purchase order
        function editPurchaseOrder(poNumber) {
            try {
                // Check if modal exists
                const modalElement = document.getElementById('po-edit-modal');
                if (!modalElement) {
                    console.error('Edit modal element not found');
                    if (window.Components) {
                        window.Components.showToast('Edit modal not available', 'error');
                    }
                    return;
                }

                // Get PO data from table row
                const poData = getPODataFromTable(poNumber);

                if (!poData) {
                    console.error('Could not retrieve PO data');
                    if (window.Components) {
                        window.Components.showToast('Could not load purchase order data', 'error');
                    }
                    return;
                }

                // Initialize and show edit modal
                const editModal = new PurchaseOrderEditModal(poData);
                editModal.show();

            } catch (error) {
                console.error('Error opening edit modal:', error);
                if (window.Components) {
                    window.Components.showToast('Error opening edit modal', 'error');
                }
            }
        }

        // Download purchase order
        function downloadPurchaseOrder(poNumber) {
            if (window.Components) {
                window.Components.showToast(`Downloading ${poNumber}...`, 'success');
            }
        }

        // Apply filters
        function applyFilters() {
            const statusFilter = document.getElementById('status-filter').value;
            const priorityFilter = document.getElementById('priority-filter').value;
            const table = document.getElementById('purchaseOrdersTable');
            const rows = table.querySelectorAll('tbody tr:not(#no-results-row)');

            rows.forEach(row => {
                let showRow = true;

                if (statusFilter) {
                    const statusBadge = row.querySelector('.status-badge');
                    const status = statusBadge.textContent.toLowerCase();
                    if (!status.includes(statusFilter.toLowerCase())) {
                        showRow = false;
                    }
                }

                if (priorityFilter && showRow) {
                    const orderClass = row.children[7].textContent.toLowerCase();
                    if (priorityFilter === 'urgent' && !orderClass.includes('urgent')) {
                        showRow = false;
                    } else if (priorityFilter === 'high' && !orderClass.includes('project')) {
                        showRow = false;
                    } else if (priorityFilter === 'normal' && !orderClass.includes('standard')) {
                        showRow = false;
                    } else if (priorityFilter === 'low' && !orderClass.includes('recurring')) {
                        showRow = false;
                    }
                }

                row.style.display = showRow ? '' : 'none';
            });
        }

        // Refresh metrics
        function refreshMetrics() {
            // Simulate metric refresh with new values
            const metrics = [
                { element: document.querySelector('.total-orders .metric-value'), target: 1267, format: null },
                { element: document.querySelector('.pending .metric-value'), target: 94, format: null },
                { element: document.querySelector('.approved .metric-value'), target: 1150, format: null },
                { element: document.querySelector('.total-value .metric-value'), target: 2450000, format: 'currency' },
                { element: document.querySelector('.rejected .metric-value'), target: 23, format: null }
            ];

            metrics.forEach(metric => {
                if (metric.element) {
                    animateValue(metric.element, metric.target, metric.format);
                }
            });
        }

        // Get PO data from table row
        function getPODataFromTable(poNumber) {
            const table = document.getElementById('purchaseOrdersTable');
            const rows = table.querySelectorAll('tbody tr:not(#no-results-row)');

            for (let row of rows) {
                const rowPONumber = row.querySelector('td:nth-child(2) strong').textContent;
                if (rowPONumber === poNumber) {
                    return {
                        poNumber: rowPONumber,
                        version: row.children[2].textContent,
                        finYear: row.children[3].textContent,
                        date: row.children[4].textContent,
                        manufacturer: row.children[5].textContent,
                        type: row.children[6].textContent,
                        orderClass: row.children[7].textContent,
                        status: row.querySelector('.status-badge').textContent,
                        amount: row.children[10].textContent,
                        attachments: JSON.parse(row.querySelector('.attachment-list').getAttribute('data-attachments') || '[]')
                    };
                }
            }

            // Return default data if not found
            return {
                poNumber: poNumber,
                version: '1.0',
                finYear: '2024-2025',
                date: new Date().toISOString().split('T')[0],
                manufacturer: '',
                type: '',
                orderClass: '',
                status: 'Draft',
                amount: '$0.00',
                attachments: []
            };
        }

        // Purchase Order Edit Modal Class
        class PurchaseOrderEditModal {
            constructor(poData) {
                this.poData = poData;
                this.currentRecordIndex = 0;
                this.totalRecords = 156;
                this.autoSaveInterval = null;
                this.isDirty = false;
                this.dragDropPanels = document.getElementById('drag-drop-panels');

                this.initializeModal();
                this.setupEventListeners();
                this.setupDragAndDrop();
                this.setupKeyboardShortcuts();
            }

            initializeModal() {
                this.modal = document.getElementById('po-edit-modal');
                this.form = document.getElementById('po-edit-form');

                // Populate form with data
                this.populateForm();
                this.populateProgressTimeline();
                this.updateRecordPosition();
            }

            populateForm() {
                const formSections = [
                    {
                        id: 'basic-info',
                        title: 'Basic Information',
                        icon: 'info',
                        fields: [
                            { name: 'poNumber', label: 'PO Number', type: 'text', value: this.poData.poNumber, readonly: true },
                            { name: 'version', label: 'Version', type: 'text', value: this.poData.version },
                            { name: 'finYear', label: 'Financial Year', type: 'text', value: this.poData.finYear },
                            { name: 'date', label: 'Date', type: 'date', value: this.poData.date },
                            { name: 'status', label: 'Status', type: 'dropzone', value: this.poData.status, panel: 'status-panel' },
                            { name: 'type', label: 'Type of Purchase', type: 'dropzone', value: this.poData.type, panel: 'type-panel' }
                        ]
                    },
                    {
                        id: 'vendor-details',
                        title: 'Vendor Details',
                        icon: 'business',
                        fields: [
                            { name: 'manufacturer', label: 'Manufacturer/Vendor', type: 'dropzone', value: this.poData.manufacturer, panel: 'vendors-panel' },
                            { name: 'contactPerson', label: 'Contact Person', type: 'text', value: 'John Smith' },
                            { name: 'contactEmail', label: 'Contact Email', type: 'email', value: '<EMAIL>' },
                            { name: 'contactPhone', label: 'Contact Phone', type: 'tel', value: '+****************' },
                            { name: 'vendorAddress', label: 'Vendor Address', type: 'textarea', value: '123 Business St, Industrial Park, NY 10001' },
                            { name: 'orderClass', label: 'Order Class', type: 'dropzone', value: this.poData.orderClass, panel: 'class-panel' }
                        ]
                    },
                    {
                        id: 'line-items',
                        title: 'Line Items',
                        icon: 'list_alt',
                        fields: [
                            { name: 'lineItems', label: 'Items', type: 'table', value: this.getLineItemsData() }
                        ]
                    },
                    {
                        id: 'financial-summary',
                        title: 'Financial Summary',
                        icon: 'account_balance',
                        fields: [
                            { name: 'subtotal', label: 'Subtotal', type: 'number', value: '10000.00' },
                            { name: 'taxRate', label: 'Tax Rate (%)', type: 'number', value: '8.5' },
                            { name: 'taxAmount', label: 'Tax Amount', type: 'number', value: '850.00', readonly: true },
                            { name: 'discount', label: 'Discount', type: 'number', value: '0.00' },
                            { name: 'totalAmount', label: 'Total Amount', type: 'number', value: this.poData.amount.replace(/[^0-9.-]/g, ''), readonly: true },
                            { name: 'currency', label: 'Currency', type: 'select', value: 'USD', options: ['USD', 'EUR', 'GBP', 'JPY'] }
                        ]
                    },
                    {
                        id: 'delivery-terms',
                        title: 'Delivery & Terms',
                        icon: 'local_shipping',
                        fields: [
                            { name: 'deliveryDate', label: 'Expected Delivery Date', type: 'date', value: '2024-02-15' },
                            { name: 'deliveryAddress', label: 'Delivery Address', type: 'textarea', value: 'Main Warehouse, 456 Storage Ave, NY 10002' },
                            { name: 'paymentTerms', label: 'Payment Terms', type: 'select', value: 'Net 30', options: ['Net 15', 'Net 30', 'Net 45', 'Net 60', 'COD'] },
                            { name: 'shippingMethod', label: 'Shipping Method', type: 'select', value: 'Standard', options: ['Standard', 'Express', 'Overnight', 'Freight'] },
                            { name: 'incoterms', label: 'Incoterms', type: 'select', value: 'FOB', options: ['FOB', 'CIF', 'EXW', 'DDP', 'DAP'] }
                        ]
                    },
                    {
                        id: 'attachments',
                        title: 'Attachments',
                        icon: 'attach_file',
                        fields: [
                            { name: 'attachments', label: 'Documents', type: 'attachments', value: this.poData.attachments }
                        ]
                    }
                ];

                this.form.innerHTML = formSections.map(section => this.createFormSection(section)).join('');

                // Initialize section collapse functionality
                this.initializeSectionCollapse();
            }

            createFormSection(section) {
                const fieldsHtml = section.fields.map(field => this.createFormField(field)).join('');

                return `
                    <div class="form-section" data-section="${section.id}">
                        <div class="section-header" onclick="toggleSection('${section.id}')">
                            <h3 class="section-title">
                                <span class="material-icons section-icon">${section.icon}</span>
                                ${section.title}
                            </h3>
                            <span class="material-icons section-toggle">expand_more</span>
                        </div>
                        <div class="section-content">
                            <div class="form-grid ${section.id === 'vendor-details' ? 'two-column' : section.id === 'financial-summary' ? 'three-column' : ''}">
                                ${fieldsHtml}
                            </div>
                        </div>
                    </div>
                `;
            }

            createFormField(field) {
                const fullWidth = field.type === 'textarea' || field.type === 'table' || field.type === 'attachments';
                const groupClass = fullWidth ? 'form-group full-width' : 'form-group';

                let inputHtml = '';

                switch (field.type) {
                    case 'dropzone':
                        inputHtml = this.createDropZone(field);
                        break;
                    case 'textarea':
                        inputHtml = `<textarea class="form-input" name="${field.name}" rows="3" ${field.readonly ? 'readonly' : ''}>${field.value}</textarea>`;
                        break;
                    case 'select':
                        const options = field.options.map(opt => `<option value="${opt}" ${opt === field.value ? 'selected' : ''}>${opt}</option>`).join('');
                        inputHtml = `<select class="form-input" name="${field.name}">${options}</select>`;
                        break;
                    case 'table':
                        inputHtml = this.createLineItemsTable(field.value);
                        break;
                    case 'attachments':
                        inputHtml = this.createAttachmentsSection(field.value);
                        break;
                    default:
                        inputHtml = `<input type="${field.type}" class="form-input" name="${field.name}" value="${field.value}" ${field.readonly ? 'readonly' : ''}>`;
                }

                return `
                    <div class="${groupClass}">
                        <label class="form-label">${field.label}</label>
                        ${inputHtml}
                    </div>
                `;
            }

            createDropZone(field) {
                const hasValue = field.value && field.value.trim() !== '';
                const zoneClass = hasValue ? 'drop-zone has-value' : 'drop-zone';

                return `
                    <div class="${zoneClass}" data-field="${field.name}" data-panel="${field.panel}">
                        <div class="drop-zone-content">
                            ${hasValue ?
                                `<div class="drop-zone-value">
                                    <span>${field.value}</span>
                                    <button type="button" class="drop-zone-remove" onclick="clearDropZone('${field.name}')">
                                        <span class="material-icons">close</span>
                                    </button>
                                </div>` :
                                `<span class="drop-zone-placeholder">Drag ${field.label.toLowerCase()} here or click to select</span>`
                            }
                        </div>
                    </div>
                `;
            }

            getLineItemsData() {
                return [
                    { item: 'Steel Pipes', description: 'High-grade steel pipes 2" diameter', quantity: 100, unit: 'pcs', rate: 25.00, amount: 2500.00 },
                    { item: 'Welding Rods', description: 'E7018 welding electrodes', quantity: 50, unit: 'kg', rate: 15.00, amount: 750.00 },
                    { item: 'Safety Equipment', description: 'Hard hats and safety glasses', quantity: 25, unit: 'sets', rate: 45.00, amount: 1125.00 }
                ];
            }

            createLineItemsTable(items) {
                const rows = items.map((item, index) => `
                    <tr>
                        <td><input type="text" class="form-input" value="${item.item}" style="border: none; background: transparent;"></td>
                        <td><input type="text" class="form-input" value="${item.description}" style="border: none; background: transparent;"></td>
                        <td><input type="number" class="form-input" value="${item.quantity}" style="border: none; background: transparent; width: 80px;"></td>
                        <td><input type="text" class="form-input" value="${item.unit}" style="border: none; background: transparent; width: 60px;"></td>
                        <td><input type="number" class="form-input" value="${item.rate}" step="0.01" style="border: none; background: transparent; width: 100px;"></td>
                        <td><strong>$${item.amount.toFixed(2)}</strong></td>
                        <td>
                            <button type="button" class="action-btn" onclick="removeLineItem(${index})" title="Remove Item">
                                <span class="material-icons">delete</span>
                            </button>
                        </td>
                    </tr>
                `).join('');

                return `
                    <table class="line-items-table">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Description</th>
                                <th>Qty</th>
                                <th>Unit</th>
                                <th>Rate</th>
                                <th>Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${rows}
                        </tbody>
                    </table>
                    <div class="add-line-item" onclick="addLineItem()">
                        <span class="material-icons">add</span>
                        <span>Add Line Item</span>
                    </div>
                `;
            }

            createAttachmentsSection(attachments) {
                const attachmentItems = attachments.map(att => `
                    <div class="attachment-item">
                        <div class="attachment-icon ${this.getFileIconClass(att.type)}">
                            <span class="material-icons">${this.getFileIconName(att.type)}</span>
                        </div>
                        <div class="attachment-info">
                            <div class="attachment-name">${att.name}</div>
                            <div class="attachment-meta">
                                <span>Size: ${att.size}</span>
                            </div>
                        </div>
                        <div class="attachment-actions">
                            <button class="attachment-action-btn" title="Download">
                                <span class="material-icons">file_download</span>
                            </button>
                            <button class="attachment-action-btn" title="Remove">
                                <span class="material-icons">delete</span>
                            </button>
                        </div>
                    </div>
                `).join('');

                return `
                    <div class="attachment-preview-list">
                        ${attachmentItems}
                        <div class="add-line-item" onclick="addAttachment()">
                            <span class="material-icons">cloud_upload</span>
                            <span>Upload New Attachment</span>
                        </div>
                    </div>
                `;
            }

            getFileIconClass(type) {
                const iconMap = {
                    'pdf': 'pdf',
                    'doc': 'doc',
                    'docx': 'doc',
                    'excel': 'excel',
                    'xlsx': 'excel'
                };
                return iconMap[type.toLowerCase()] || 'default';
            }

            getFileIconName(type) {
                const iconMap = {
                    'pdf': 'picture_as_pdf',
                    'doc': 'description',
                    'docx': 'description',
                    'excel': 'table_chart',
                    'xlsx': 'table_chart'
                };
                return iconMap[type.toLowerCase()] || 'insert_drive_file';
            }

            populateProgressTimeline() {
                const timeline = document.getElementById('progress-timeline');
                const steps = [
                    { id: 'draft', title: 'Draft', status: 'completed', user: 'JS', timestamp: 'Jan 15, 2024 at 9:30 AM', name: 'John Smith' },
                    { id: 'submitted', title: 'Submitted', status: 'completed', user: 'JS', timestamp: 'Jan 15, 2024 at 2:15 PM', name: 'John Smith' },
                    { id: 'review', title: 'Under Review', status: 'in-progress', user: 'MJ', timestamp: 'Jan 16, 2024 at 10:45 AM', name: 'Mike Johnson' },
                    { id: 'approved', title: 'Approved', status: 'pending', user: 'SB', timestamp: '', name: 'Sarah Brown' },
                    { id: 'progress', title: 'In Progress', status: 'pending', user: 'DW', timestamp: '', name: 'David Wilson' },
                    { id: 'completed', title: 'Completed', status: 'pending', user: 'JS', timestamp: '', name: 'John Smith' }
                ];

                timeline.innerHTML = steps.map(step => `
                    <div class="timeline-step" data-step="${step.id}">
                        <div class="step-circle ${step.status}" title="${step.name}">
                            ${step.status === 'completed' ? '<span class="material-icons">check</span>' :
                              step.status === 'in-progress' ? '<span class="material-icons">schedule</span>' :
                              `<div class="step-avatar">${step.user}</div>`}
                        </div>
                        <div class="step-info">
                            <div class="step-title">${step.title}</div>
                            ${step.timestamp ? `<div class="step-timestamp">${step.timestamp}</div>` : ''}
                            <div class="step-user">${step.name}</div>
                        </div>
                    </div>
                `).join('');
            }

            updateRecordPosition() {
                const positionElement = document.getElementById('record-position');
                positionElement.textContent = `Record ${this.currentRecordIndex + 1} of ${this.totalRecords}`;
            }

            initializeSectionCollapse() {
                // All sections start expanded
                document.querySelectorAll('.section-content').forEach(content => {
                    content.style.maxHeight = content.scrollHeight + 'px';
                });
            }

            setupEventListeners() {
                // Navigation buttons
                document.getElementById('first-record').addEventListener('click', () => this.navigateToRecord(0));
                document.getElementById('prev-record').addEventListener('click', () => this.navigateToRecord(this.currentRecordIndex - 1));
                document.getElementById('next-record').addEventListener('click', () => this.navigateToRecord(this.currentRecordIndex + 1));
                document.getElementById('last-record').addEventListener('click', () => this.navigateToRecord(this.totalRecords - 1));

                // Footer buttons
                document.getElementById('print-export-btn').addEventListener('click', () => this.printExport());
                document.getElementById('save-draft-btn').addEventListener('click', () => this.saveDraft());
                document.getElementById('save-close-btn').addEventListener('click', () => this.saveAndClose());

                // Form change detection
                this.form.addEventListener('input', () => this.markDirty());
                this.form.addEventListener('change', () => this.markDirty());

                // Drop zone click handlers
                document.querySelectorAll('.drop-zone').forEach(zone => {
                    zone.addEventListener('click', (e) => {
                        if (!e.target.closest('.drop-zone-remove')) {
                            this.showDragPanel(zone.getAttribute('data-panel'));
                        }
                    });
                });
            }

            setupDragAndDrop() {
                // Setup drag chips
                document.querySelectorAll('.drag-chip').forEach(chip => {
                    chip.addEventListener('dragstart', (e) => {
                        e.dataTransfer.setData('text/plain', chip.getAttribute('data-value'));
                        e.dataTransfer.setData('text/html', chip.innerHTML);
                        chip.classList.add('dragging');
                    });

                    chip.addEventListener('dragend', () => {
                        chip.classList.remove('dragging');
                    });
                });

                // Setup drop zones
                document.querySelectorAll('.drop-zone').forEach(zone => {
                    zone.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        zone.classList.add('drag-over');
                    });

                    zone.addEventListener('dragleave', () => {
                        zone.classList.remove('drag-over');
                    });

                    zone.addEventListener('drop', (e) => {
                        e.preventDefault();
                        zone.classList.remove('drag-over');

                        const value = e.dataTransfer.getData('text/plain');
                        const html = e.dataTransfer.getData('text/html');

                        this.updateDropZone(zone, value, html);
                        this.markDirty();
                    });
                });
            }

            setupKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    if (this.modal.classList.contains('active')) {
                        switch (e.key) {
                            case 'Escape':
                                this.close();
                                break;
                            case 's':
                                if (e.ctrlKey) {
                                    e.preventDefault();
                                    this.saveDraft();
                                }
                                break;
                            case 'p':
                                if (e.ctrlKey) {
                                    e.preventDefault();
                                    this.printExport();
                                }
                                break;
                            case 'ArrowLeft':
                                if (e.ctrlKey) {
                                    e.preventDefault();
                                    this.navigateToRecord(this.currentRecordIndex - 1);
                                }
                                break;
                            case 'ArrowRight':
                                if (e.ctrlKey) {
                                    e.preventDefault();
                                    this.navigateToRecord(this.currentRecordIndex + 1);
                                }
                                break;
                            case 'Home':
                                if (e.ctrlKey) {
                                    e.preventDefault();
                                    this.navigateToRecord(0);
                                }
                                break;
                            case 'End':
                                if (e.ctrlKey) {
                                    e.preventDefault();
                                    this.navigateToRecord(this.totalRecords - 1);
                                }
                                break;
                        }
                    }
                });
            }

            showDragPanel(panelId) {
                this.dragDropPanels.classList.add('visible');

                // Hide all panels first
                document.querySelectorAll('.drag-panel').forEach(panel => {
                    panel.style.display = 'none';
                });

                // Show specific panel
                const targetPanel = document.getElementById(panelId);
                if (targetPanel) {
                    targetPanel.style.display = 'block';
                }

                // Auto-hide after 10 seconds
                setTimeout(() => {
                    this.dragDropPanels.classList.remove('visible');
                }, 10000);
            }

            updateDropZone(zone, value, html) {
                const fieldName = zone.getAttribute('data-field');
                const content = zone.querySelector('.drop-zone-content');

                content.innerHTML = `
                    <div class="drop-zone-value">
                        <span>${value}</span>
                        <button type="button" class="drop-zone-remove" onclick="clearDropZone('${fieldName}')">
                            <span class="material-icons">close</span>
                        </button>
                    </div>
                `;

                zone.classList.add('has-value');

                // Update hidden input if exists
                const hiddenInput = this.form.querySelector(`input[name="${fieldName}"]`);
                if (hiddenInput) {
                    hiddenInput.value = value;
                }
            }

            navigateToRecord(index) {
                if (index < 0 || index >= this.totalRecords) return;

                this.currentRecordIndex = index;
                this.updateRecordPosition();

                // Update navigation button states
                document.getElementById('first-record').disabled = index === 0;
                document.getElementById('prev-record').disabled = index === 0;
                document.getElementById('next-record').disabled = index === this.totalRecords - 1;
                document.getElementById('last-record').disabled = index === this.totalRecords - 1;

                // Simulate loading new record data
                if (window.Components) {
                    window.Components.showToast(`Loading record ${index + 1}...`, 'info');
                }
            }

            markDirty() {
                this.isDirty = true;
                this.showAutoSaveIndicator();
            }

            showAutoSaveIndicator() {
                const indicator = document.getElementById('auto-save-status');
                indicator.classList.add('visible');

                // Auto-hide after 3 seconds
                setTimeout(() => {
                    indicator.classList.remove('visible');
                }, 3000);
            }

            startAutoSave() {
                this.autoSaveInterval = setInterval(() => {
                    if (this.isDirty) {
                        this.saveDraft(true); // Silent save
                    }
                }, 30000); // Every 30 seconds
            }

            stopAutoSave() {
                if (this.autoSaveInterval) {
                    clearInterval(this.autoSaveInterval);
                    this.autoSaveInterval = null;
                }
            }

            saveDraft(silent = false) {
                const formData = new FormData(this.form);

                // Simulate save operation
                if (!silent && window.Components) {
                    window.Components.showToast('Draft saved successfully', 'success');
                }

                this.isDirty = false;
                this.showAutoSaveIndicator();
            }

            saveAndClose() {
                this.saveDraft();

                if (window.Components) {
                    window.Components.showToast('Purchase order saved successfully', 'success');
                }

                setTimeout(() => {
                    this.close();
                }, 1000);
            }

            printExport() {
                if (window.Components) {
                    window.Components.showToast('Generating export...', 'info');
                }
            }

            show() {
                this.modal.classList.add('active');
                this.startAutoSave();

                // Focus first input
                setTimeout(() => {
                    const firstInput = this.form.querySelector('input:not([readonly])');
                    if (firstInput) {
                        firstInput.focus();
                    }
                }, 300);
            }

            close() {
                if (this.isDirty) {
                    if (confirm('You have unsaved changes. Are you sure you want to close?')) {
                        this.modal.classList.remove('active');
                        this.stopAutoSave();
                        this.dragDropPanels.classList.remove('visible');
                    }
                } else {
                    this.modal.classList.remove('active');
                    this.stopAutoSave();
                    this.dragDropPanels.classList.remove('visible');
                }
            }
        }

        // Enhanced value animation with formatting
        function animateValue(element, target, format = null) {
            let current = 0;
            const increment = target / 60; // 60 frames for smooth animation
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                if (format === 'currency') {
                    element.textContent = '$' + (current / 1000000).toFixed(1) + 'M';
                } else {
                    element.textContent = Math.floor(current).toLocaleString();
                }
            }, 16); // ~60fps
        }

        // Initialize metric card interactions
        function initializeMetricCardInteractions() {
            document.querySelectorAll('.metric-card').forEach(card => {
                card.addEventListener('click', function() {
                    const metric = this.getAttribute('data-metric');
                    showMetricDetails(metric);
                });

                // Add pulse effect on hover
                card.addEventListener('mouseenter', function() {
                    const icon = this.querySelector('.metric-icon');
                    icon.style.transform = 'scale(1.1)';
                    icon.style.transition = 'transform 0.3s ease';
                });

                card.addEventListener('mouseleave', function() {
                    const icon = this.querySelector('.metric-icon');
                    icon.style.transform = 'scale(1)';
                });
            });
        }

        // Show detailed metric information
        function showMetricDetails(metric) {
            const details = {
                total: {
                    title: 'Total Orders Analysis',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #f5f5f5; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #2196F3;">1,234</div>
                                <div style="font-size: 0.875rem; color: #666;">Total Orders</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #f5f5f5; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #4CAF50;">+12.5%</div>
                                <div style="font-size: 0.875rem; color: #666;">Growth Rate</div>
                            </div>
                        </div>
                        <p>Your purchase order volume has increased by 12.5% compared to last month, indicating strong business growth.</p>
                        <p><strong>Breakdown:</strong></p>
                        <ul>
                            <li>New Orders: 456</li>
                            <li>Recurring Orders: 778</li>
                            <li>Average Order Value: $1,945</li>
                        </ul>
                    `
                },
                pending: {
                    title: 'Pending Orders Analysis',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #fff3e0; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #FF9800;">89</div>
                                <div style="font-size: 0.875rem; color: #666;">Pending Orders</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #fff3e0; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #FF9800;">2.3</div>
                                <div style="font-size: 0.875rem; color: #666;">Avg Days</div>
                            </div>
                        </div>
                        <p>89 orders are currently pending approval with an average processing time of 2.3 days.</p>
                        <p><strong>Priority Actions:</strong></p>
                        <ul>
                            <li>12 orders over $10,000 need immediate review</li>
                            <li>23 orders from preferred suppliers</li>
                            <li>54 standard orders in queue</li>
                        </ul>
                    `
                },
                approved: {
                    title: 'Approved Orders Analysis',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #e8f5e8; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #4CAF50;">1,145</div>
                                <div style="font-size: 0.875rem; color: #666;">Approved Orders</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #e8f5e8; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #4CAF50;">92.8%</div>
                                <div style="font-size: 0.875rem; color: #666;">Success Rate</div>
                            </div>
                        </div>
                        <p>Excellent approval rate of 92.8% indicates efficient procurement processes and strong supplier relationships.</p>
                        <p><strong>Performance Metrics:</strong></p>
                        <ul>
                            <li>Average approval time: 1.2 days</li>
                            <li>Auto-approved orders: 67%</li>
                            <li>Manual review required: 33%</li>
                        </ul>
                    `
                },
                value: {
                    title: 'Total Value Analysis',
                    content: `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: #f3e5f5; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #9C27B0;">$2.4M</div>
                                <div style="font-size: 0.875rem; color: #666;">Total Value</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #f3e5f5; border-radius: 8px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #9C27B0;">78%</div>
                                <div style="font-size: 0.875rem; color: #666;">Budget Used</div>
                            </div>
                        </div>
                        <p>Total purchase order value of $2.4M represents 78% of annual budget with strong ROI performance.</p>
                        <p><strong>Financial Breakdown:</strong></p>
                        <ul>
                            <li>Raw Materials: $1.2M (50%)</li>
                            <li>Equipment: $720K (30%)</li>
                            <li>Services: $480K (20%)</li>
                        </ul>
                    `
                }
            };

            if (window.Components && details[metric]) {
                window.Components.createModal(
                    details[metric].title,
                    details[metric].content,
                    { width: '600px' }
                );
            }
        }

        // Refresh metrics with animation
        function refreshMetrics() {
            if (window.Components) {
                window.Components.showToast('Refreshing metrics...', 'info');
            }

            // Add loading state
            document.querySelectorAll('.metric-card').forEach(card => {
                card.style.opacity = '0.6';
                card.style.transform = 'scale(0.98)';
            });

            // Simulate data refresh
            setTimeout(() => {
                document.querySelectorAll('.metric-card').forEach(card => {
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1)';
                });

                // Re-animate values
                initializeMetricAnimations();

                if (window.Components) {
                    window.Components.showToast('Metrics updated successfully!', 'success');
                }
            }, 1500);
        }

        function getPurchaseOrderForm() {
            return `
                <form class="form-container" id="po-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Supplier *</label>
                            <select class="form-input" required>
                                <option value="">Select Supplier</option>
                                <option value="abc">ABC Suppliers Ltd.</option>
                                <option value="xyz">XYZ Trading Co.</option>
                                <option value="def">DEF Industries</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Expected Delivery Date *</label>
                            <input type="date" class="form-input" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Description</label>
                        <textarea class="form-input" rows="3" placeholder="Purchase order description..."></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Items</label>
                        <div class="items-container">
                            <div class="item-row" style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr auto; gap: 1rem; align-items: end; margin-bottom: 1rem;">
                                <div>
                                    <label class="form-label">Item</label>
                                    <input type="text" class="form-input" placeholder="Item name">
                                </div>
                                <div>
                                    <label class="form-label">Quantity</label>
                                    <input type="number" class="form-input" placeholder="0">
                                </div>
                                <div>
                                    <label class="form-label">Unit Price</label>
                                    <input type="number" class="form-input" placeholder="0.00" step="0.01">
                                </div>
                                <div>
                                    <label class="form-label">Total</label>
                                    <input type="number" class="form-input" placeholder="0.00" readonly>
                                </div>
                                <button type="button" class="btn btn-secondary" style="height: 40px;">
                                    <span class="material-icons">add</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            `;
        }

        function savePurchaseOrder() {
            if (window.Components) {
                window.Components.showToast('Purchase order created successfully!', 'success');
                // Close modal
                const modal = document.querySelector('.modal-backdrop');
                if (modal) {
                    window.Components.closeModal(modal);
                }
            }
        }

        function handleTableAction(e) {
            const button = e.currentTarget;
            const action = button.classList.contains('view') ? 'view' :
                          button.classList.contains('edit') ? 'edit' :
                          button.classList.contains('delete') ? 'delete' : 'download';

            const row = button.closest('tr');
            const poNumber = row.querySelector('td:first-child strong').textContent;

            switch (action) {
                case 'view':
                    if (window.Components) {
                        window.Components.showToast(`Viewing details for ${poNumber}`, 'info');
                    }
                    break;
                case 'edit':
                    if (window.Components) {
                        window.Components.showToast(`Editing ${poNumber}`, 'info');
                    }
                    break;
                case 'delete':
                    if (window.Components) {
                        window.Components.confirm(
                            `Are you sure you want to delete ${poNumber}?`,
                            () => {
                                window.Components.showToast(`${poNumber} deleted successfully`, 'success');
                                row.remove();
                            }
                        );
                    }
                    break;
                case 'download':
                    if (window.Components) {
                        window.Components.showToast(`Downloading PDF for ${poNumber}`, 'info');
                    }
                    break;
            }
        }

        function applyFilters() {
            // TODO: Implement filtering logic
            if (window.Components) {
                window.Components.showToast('Filters applied', 'info');
            }
        }

        // Global functions for modal interactions
        function toggleSection(sectionId) {
            const section = document.querySelector(`[data-section="${sectionId}"]`);
            const content = section.querySelector('.section-content');
            const toggle = section.querySelector('.section-toggle');

            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                content.style.maxHeight = content.scrollHeight + 'px';
                toggle.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                content.style.maxHeight = '0';
                toggle.classList.add('collapsed');
            }
        }

        function clearDropZone(fieldName) {
            const zone = document.querySelector(`[data-field="${fieldName}"]`);
            const content = zone.querySelector('.drop-zone-content');

            content.innerHTML = `<span class="drop-zone-placeholder">Drag ${fieldName.toLowerCase()} here or click to select</span>`;
            zone.classList.remove('has-value');

            // Clear hidden input if exists
            const hiddenInput = document.querySelector(`input[name="${fieldName}"]`);
            if (hiddenInput) {
                hiddenInput.value = '';
            }
        }

        function addLineItem() {
            if (window.Components) {
                window.Components.showToast('Add line item functionality coming soon!', 'info');
            }
        }

        function removeLineItem(index) {
            if (window.Components) {
                window.Components.showToast(`Removing line item ${index + 1}...`, 'info');
            }
        }

        function addAttachment() {
            if (window.Components) {
                window.Components.showToast('Upload attachment functionality coming soon!', 'info');
            }
        }

        function closePOEditModal() {
            const modal = document.getElementById('po-edit-modal');
            if (modal.classList.contains('active')) {
                modal.classList.remove('active');

                // Hide drag panels
                const dragPanels = document.getElementById('drag-drop-panels');
                dragPanels.classList.remove('visible');
            }
        }

        // Initialize View Modes
        function initializeViewModes() {
            const viewModeButtons = document.querySelectorAll('.view-mode-btn');

            viewModeButtons.forEach(btn => {
                btn.addEventListener('click', () => {
                    const viewType = btn.getAttribute('data-view');
                    switchView(viewType);

                    // Update active button
                    viewModeButtons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                });
            });

            // Initialize with table view
            populateAllViews();
        }

        // Switch between different view modes
        function switchView(viewType) {
            // Hide all view containers
            document.querySelectorAll('.view-container').forEach(container => {
                container.classList.remove('active');
            });

            // Show selected view
            const targetView = document.getElementById(`${viewType}-view`);
            if (targetView) {
                targetView.classList.add('active');

                // Update queue columns visibility
                const queueColumns = document.getElementById('queue-columns');
                if (viewType === 'grid' || viewType === 'card') {
                    queueColumns.style.display = 'grid';
                } else {
                    queueColumns.style.display = 'none';
                }

                // Refresh view data
                refreshCurrentView();
            }
        }

        // Refresh current view data
        function refreshCurrentView() {
            const activeView = document.querySelector('.view-container.active');
            if (!activeView) return;

            const viewId = activeView.id;

            switch (viewId) {
                case 'grid-view':
                    populateGridView();
                    break;
                case 'card-view':
                    populateCardView();
                    break;
                case 'list-view':
                    populateListView();
                    break;
                case 'compact-view':
                    populateCompactView();
                    break;
                default:
                    // Table view is already populated
                    break;
            }
        }

        // Populate all views with data
        function populateAllViews() {
            populateGridView();
            populateCardView();
            populateListView();
            populateCompactView();
        }

        // Populate Grid View
        function populateGridView() {
            const gridContainer = document.getElementById('po-grid');
            const table = document.getElementById('purchaseOrdersTable');
            const rows = table.querySelectorAll('tbody tr:not(#no-results-row)');

            gridContainer.innerHTML = '';

            rows.forEach(row => {
                const poData = extractRowData(row);
                const gridItem = createGridItem(poData);
                gridContainer.appendChild(gridItem);
            });
        }

        // Populate Card View
        function populateCardView() {
            const cardsContainer = document.getElementById('po-cards');
            const table = document.getElementById('purchaseOrdersTable');
            const rows = table.querySelectorAll('tbody tr:not(#no-results-row)');

            cardsContainer.innerHTML = '';

            rows.forEach(row => {
                const poData = extractRowData(row);
                const cardItem = createCardItem(poData);
                cardsContainer.appendChild(cardItem);
            });
        }

        // Populate List View
        function populateListView() {
            const listContainer = document.getElementById('po-list');
            const table = document.getElementById('purchaseOrdersTable');
            const rows = table.querySelectorAll('tbody tr:not(#no-results-row)');

            listContainer.innerHTML = '';

            rows.forEach(row => {
                const poData = extractRowData(row);
                const listItem = createListItem(poData);
                listContainer.appendChild(listItem);
            });
        }

        // Populate Compact View
        function populateCompactView() {
            const compactContainer = document.getElementById('po-compact');
            const table = document.getElementById('purchaseOrdersTable');
            const rows = table.querySelectorAll('tbody tr:not(#no-results-row)');

            compactContainer.innerHTML = '';

            rows.forEach(row => {
                const poData = extractRowData(row);
                const compactItem = createCompactItem(poData);
                compactContainer.appendChild(compactItem);
            });
        }

        // Extract data from table row
        function extractRowData(row) {
            return {
                poNumber: row.querySelector('td:nth-child(2) strong').textContent,
                version: row.children[2].textContent,
                finYear: row.children[3].textContent,
                date: row.children[4].textContent,
                manufacturer: row.children[5].textContent,
                type: row.children[6].textContent,
                orderClass: row.children[7].textContent,
                status: row.querySelector('.status-badge').textContent,
                amount: row.children[10].textContent,
                queue: row.getAttribute('data-queue'),
                locked: row.getAttribute('data-locked') === 'true'
            };
        }

        // Create Grid Item
        function createGridItem(data) {
            const item = document.createElement('div');
            item.className = 'po-grid-item';
            item.draggable = true;
            item.setAttribute('data-po-number', data.poNumber);

            item.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h3 style="margin: 0; color: var(--color-primary);">${data.poNumber}</h3>
                    <div class="status-badge ${getStatusClass(data.status)}">${data.status}</div>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin-bottom: 1rem; font-size: 0.875rem;">
                    <div><strong>Version:</strong> ${data.version}</div>
                    <div><strong>Year:</strong> ${data.finYear}</div>
                    <div><strong>Date:</strong> ${data.date}</div>
                    <div><strong>Type:</strong> ${data.type}</div>
                </div>
                <div style="margin-bottom: 1rem;">
                    <div style="font-weight: 600; margin-bottom: 0.25rem;">Manufacturer</div>
                    <div style="font-size: 0.875rem; color: var(--color-secondary);">${data.manufacturer}</div>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="font-size: 1.25rem; font-weight: bold; color: var(--color-primary);">${data.amount}</div>
                    <div style="display: flex; gap: 0.5rem;">
                        ${createLockButton(data.locked, data.poNumber)}
                        ${createActionButtons(data.poNumber)}
                    </div>
                </div>
            `;

            addDragListeners(item, data);
            addClickListeners(item);

            return item;
        }

        // Create Card Item
        function createCardItem(data) {
            const item = document.createElement('div');
            item.className = 'po-card-item';
            item.draggable = true;
            item.setAttribute('data-po-number', data.poNumber);

            item.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                    <div>
                        <h2 style="margin: 0 0 0.5rem 0; color: var(--color-primary);">${data.poNumber}</h2>
                        <div style="color: var(--color-secondary);">Version ${data.version} • ${data.finYear}</div>
                    </div>
                    <div class="status-badge ${getStatusClass(data.status)}">${data.status}</div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
                    <div>
                        <div style="font-weight: 600; margin-bottom: 0.5rem; color: var(--color-primary);">Purchase Details</div>
                        <div style="margin-bottom: 0.5rem;"><strong>Date:</strong> ${data.date}</div>
                        <div style="margin-bottom: 0.5rem;"><strong>Type:</strong> ${data.type}</div>
                        <div><strong>Class:</strong> ${data.orderClass}</div>
                    </div>
                    <div>
                        <div style="font-weight: 600; margin-bottom: 0.5rem; color: var(--color-primary);">Vendor Information</div>
                        <div style="margin-bottom: 0.5rem;">${data.manufacturer}</div>
                        <div style="font-size: 2rem; font-weight: bold; color: var(--color-primary);">${data.amount}</div>
                    </div>
                </div>

                <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 1rem; border-top: 1px solid var(--color-border);">
                    <div style="display: flex; gap: 0.5rem;">
                        ${createLockButton(data.locked, data.poNumber)}
                        ${createActionButtons(data.poNumber)}
                    </div>
                    <div style="font-size: 0.875rem; color: var(--color-secondary);">
                        Queue: ${data.queue.charAt(0).toUpperCase() + data.queue.slice(1)}
                    </div>
                </div>
            `;

            addDragListeners(item, data);
            addClickListeners(item);

            return item;
        }

        // Create List Item
        function createListItem(data) {
            const item = document.createElement('div');
            item.className = 'po-list-item';
            item.draggable = true;
            item.setAttribute('data-po-number', data.poNumber);

            item.innerHTML = `
                <div style="display: flex; align-items: center; gap: 1rem; flex: 1;">
                    <div style="min-width: 120px;">
                        <div style="font-weight: 600; color: var(--color-primary);">${data.poNumber}</div>
                        <div style="font-size: 0.75rem; color: var(--color-secondary);">v${data.version}</div>
                    </div>
                    <div style="min-width: 150px;">
                        <div style="font-weight: 500;">${data.manufacturer}</div>
                        <div style="font-size: 0.75rem; color: var(--color-secondary);">${data.type}</div>
                    </div>
                    <div style="min-width: 100px;">
                        <div class="status-badge ${getStatusClass(data.status)}">${data.status}</div>
                    </div>
                    <div style="min-width: 80px; text-align: center;">
                        <div style="font-size: 0.75rem; color: var(--color-secondary);">${data.date}</div>
                    </div>
                    <div style="min-width: 100px; text-align: right;">
                        <div style="font-weight: 600; color: var(--color-primary);">${data.amount}</div>
                    </div>
                    <div style="display: flex; gap: 0.25rem; margin-left: auto;">
                        ${createLockButton(data.locked, data.poNumber, 'small')}
                        ${createActionButtons(data.poNumber, 'small')}
                    </div>
                </div>
            `;

            addDragListeners(item, data);
            addClickListeners(item);

            return item;
        }

        // Create Compact Item
        function createCompactItem(data) {
            const item = document.createElement('div');
            item.className = 'po-compact-item';
            item.draggable = true;
            item.setAttribute('data-po-number', data.poNumber);

            item.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.75rem; flex: 1;">
                    <div style="min-width: 100px; font-weight: 600; color: var(--color-primary);">${data.poNumber}</div>
                    <div style="min-width: 120px; font-size: 0.75rem;">${data.manufacturer}</div>
                    <div style="min-width: 80px;">
                        <div class="status-badge ${getStatusClass(data.status)}" style="font-size: 0.625rem; padding: 1px 4px;">${data.status}</div>
                    </div>
                    <div style="min-width: 70px; font-size: 0.75rem; text-align: center;">${data.date}</div>
                    <div style="min-width: 80px; font-weight: 600; text-align: right;">${data.amount}</div>
                    <div style="display: flex; gap: 0.25rem; margin-left: auto;">
                        ${createLockButton(data.locked, data.poNumber, 'mini')}
                        ${createActionButtons(data.poNumber, 'mini')}
                    </div>
                </div>
            `;

            addDragListeners(item, data);
            addClickListeners(item);

            return item;
        }

        // Create lock button
        function createLockButton(locked, poNumber, size = 'normal') {
            const sizeClass = size === 'small' ? 'style="width: 24px; height: 24px; font-size: 0.75rem;"' :
                             size === 'mini' ? 'style="width: 20px; height: 20px; font-size: 0.625rem;"' : '';

            return `
                <button class="action-btn lock-btn ${locked ? 'locked' : 'unlocked'}"
                        data-action="${locked ? 'unlock' : 'lock'}"
                        data-po-number="${poNumber}"
                        title="${locked ? 'Unlock Task' : 'Lock Task'}"
                        ${sizeClass}>
                    <span class="material-icons">${locked ? 'lock' : 'lock_open'}</span>
                </button>
            `;
        }

        // Create action buttons
        function createActionButtons(poNumber, size = 'normal') {
            const sizeClass = size === 'small' ? 'style="width: 24px; height: 24px; font-size: 0.75rem;"' :
                             size === 'mini' ? 'style="width: 20px; height: 20px; font-size: 0.625rem;"' : '';

            return `
                <button class="action-btn view" data-action="view" title="View Details" ${sizeClass}>
                    <span class="material-icons">visibility</span>
                </button>
                <button class="action-btn edit" data-action="edit" title="Edit" ${sizeClass}>
                    <span class="material-icons">edit</span>
                </button>
                <button class="action-btn download" data-action="download" title="Download PDF" ${sizeClass}>
                    <span class="material-icons">file_download</span>
                </button>
            `;
        }

        // Add drag listeners to items
        function addDragListeners(item, data) {
            item.addEventListener('dragstart', (e) => {
                item.classList.add('dragging');
                e.dataTransfer.setData('application/json', JSON.stringify(data));
            });

            item.addEventListener('dragend', () => {
                item.classList.remove('dragging');
            });
        }

        // Add click listeners to items
        function addClickListeners(item) {
            // Selection on click
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.action-btn')) {
                    item.classList.toggle('selected');
                    updateBulkActionButtons();
                }
            });

            // Action button handlers
            item.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', handleTableAction);
            });
        }

        // Update bulk action buttons
        function updateBulkActionButtons() {
            const currentView = document.querySelector('.view-container.active');
            const selectedCount = currentView.querySelectorAll('.selected').length;
            const bulkMoveBtn = document.getElementById('bulk-move-btn');

            bulkMoveBtn.disabled = selectedCount === 0;
            bulkMoveBtn.innerHTML = `
                <span class="material-icons">move_to_inbox</span>
                Move Selected${selectedCount > 0 ? ` (${selectedCount})` : ''}
            `;
        }
    </script>
</body>
</html>
