/* Layout-specific styles for the inventory management system */

/* App container */
.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--color-light);
}

/* Header styles */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-xl);
    background-color: var(--color-white);
    border-bottom: 1px solid var(--color-border);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    flex: 1;
}

.header-center {
    display: flex;
    align-items: center;
    flex: 2;
    justify-content: center;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    justify-content: flex-end;
}

/* Logo styles */
.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo .material-icons {
    font-size: 2rem;
    color: var(--color-primary);
}

.logo-text h1 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0;
    color: var(--color-primary);
}

.logo-text p {
    font-size: var(--font-size-xs);
    font-weight: 500;
    margin: 0;
    color: var(--color-tertiary);
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

/* Search container */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 500px;
    background-color: var(--color-light);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-sm) var(--spacing-md);
    gap: var(--spacing-sm);
}

.search-container .material-icons {
    color: var(--color-tertiary);
    font-size: 1.25rem;
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    outline: none;
}

.search-input::placeholder {
    color: var(--color-quaternary);
}

.search-shortcut {
    font-size: var(--font-size-xs);
    color: var(--color-quaternary);
    background-color: var(--color-white);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--color-border);
}

/* User profile */
.user-profile {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.user-profile:hover {
    background-color: var(--color-hover);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--color-primary);
    color: var(--color-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--color-primary);
    line-height: 1.2;
}

.user-role {
    font-size: var(--font-size-xs);
    color: var(--color-tertiary);
    line-height: 1.2;
}

/* Navigation styles */
.navigation {
    background-color: var(--color-white);
    border-bottom: 1px solid var(--color-border);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 80px;
    z-index: 90;
}

.nav-container {
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-xl);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.nav-container::-webkit-scrollbar {
    display: none;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    text-decoration: none;
    color: var(--color-tertiary);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border-bottom: 3px solid transparent;
    transition: var(--transition);
    white-space: nowrap;
    position: relative;
}

.nav-item:hover {
    color: var(--color-primary);
    background-color: var(--color-hover);
}

.nav-item.active {
    color: var(--color-primary);
    border-bottom-color: var(--color-primary);
    background-color: var(--color-hover);
}

.nav-item .material-icons {
    font-size: 1.125rem;
}

/* Main content */
.main-content {
    flex: 1;
    padding: var(--spacing-sm);
    max-width: 1600px;
    margin: 0 auto;
    width: 100%;
}

.page-header {
    margin-bottom: var(--spacing-xl);
}

.page-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: var(--spacing-xs);
}

.page-header p {
    color: var(--color-tertiary);
    font-size: var(--font-size-base);
    margin: 0;
}

/* Dashboard grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.dashboard-card {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--color-border);
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.card-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--color-primary);
    margin: 0;
}

.card-header .material-icons {
    color: var(--color-tertiary);
    font-size: 1.5rem;
}

.card-content {
    display: flex;
    align-items: baseline;
    gap: var(--spacing-sm);
}

.metric-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--color-primary);
    line-height: 1;
}

.metric-change {
    font-size: var(--font-size-sm);
    font-weight: 500;
    padding: 2px 8px;
    border-radius: var(--border-radius-sm);
}

.metric-change.positive {
    color: var(--color-success);
    background-color: rgba(76, 175, 80, 0.1);
}

.metric-change.negative {
    color: var(--color-error);
    background-color: rgba(244, 67, 54, 0.1);
}

.metric-change.neutral {
    color: var(--color-tertiary);
    background-color: var(--color-light);
}

/* Responsive design */
@media (max-width: 1024px) {
    .header {
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .main-content {
        padding: var(--spacing-lg);
    }
    
    .nav-container {
        padding: 0 var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }
    
    .header-left,
    .header-center,
    .header-right {
        flex: none;
        width: 100%;
    }
    
    .header-center {
        order: 3;
    }
    
    .header-right {
        order: 2;
        justify-content: space-between;
    }
    
    .logo-text h1 {
        font-size: var(--font-size-lg);
    }
    
    .user-info {
        display: none;
    }
    
    .navigation {
        top: 140px;
    }
    
    .nav-item {
        padding: var(--spacing-md);
    }
    
    .nav-item span:last-child {
        display: none;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .main-content {
        padding: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .search-container {
        max-width: none;
    }
    
    .search-shortcut {
        display: none;
    }
    
    .metric-value {
        font-size: var(--font-size-2xl);
    }
}
